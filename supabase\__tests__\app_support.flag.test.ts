import { describe, test, expect } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockService } from "./mocks/app_provider.service";
import { mockFlag } from "./mocks/app_support.flag";
import { serviceClient } from "./utils/client";

createSetupHooks();

describe("Flagging System", () => {
  const flagger = mockCustomer();
  const flaggedProvider = mockProvider();
  const admin = mockAdmin();
  const service = mockService(flaggedProvider);

  const flag = mockFlag({
    flagger,
    flagged: flaggedProvider,
    flag: {
      p_reason: "Inappropriate content",
      p_flagged_service_id: service.providerServiceId
    }
  });

  test("user with 'app_support.flag.create' capability can create a flag", () => {
    expect(flag.id).toBeDefined();
  });

  test("admin can view all flags", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!flag.id) throw new Error("Flag ID is undefined");

    const { data, error } = await admin.client
      .schema("app_support")
      .from("flag")
      .select("*")
      .eq("id", flag.id)
      .single();

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(data?.id).toBe(flag.id);
  });

  test("non-admin users cannot view flags", async () => {
    if (!flagger.client) throw new Error("Flagger client is undefined");
    if (!flag.id) throw new Error("Flag ID is undefined");

    const { data, error } = await flagger.client
      .schema("app_support")
      .from("flag")
      .select("*")
      .eq("id", flag.id)
      .single();

    expect(error).toBeDefined();
    expect(data).toBeNull();
  });

  test("admin can delete a flag", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!flag.id) throw new Error("Flag ID is undefined");

    const { error } = await admin.client
      .schema("app_support")
      .from("flag")
      .delete()
      .eq("id", flag.id);

    expect(error).toBeNull();

    const { data: checkData } = await serviceClient
      .schema("app_support")
      .from("flag")
      .select("*")
      .eq("id", flag.id)
      .single();

    expect(checkData).toBeNull();
  });
});
