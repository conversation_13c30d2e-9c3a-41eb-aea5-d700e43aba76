# Missing Platform Features

This document outlines features that are not yet fully implemented in the database schema, based on a review of the `development-status.md` and the `supabase/migrations` files.

## User Management

- **Explicit Ban Mechanism**: While users can be implicitly banned by deleting their accounts, there is no explicit `is_banned` flag or `banned_users` table for temporary or permanent bans with reasons.
- **Admin View for Client Transaction History**: There is no dedicated view for admins to easily see a client's complete transaction history (deposits, orders, transfers).

## Service Management

- **Featured Providers**: There is no mechanism in the database (e.g., an `is_featured` flag on `app_provider.profile`) to highlight specific providers.
- **Advanced Search Sorting**: While sorting by rating and price is possible with existing data, dedicated indexes for these sorting operations on a search view/function would improve performance.

## Communication

- **Typing Indicators**: The `app_chat` schema does not include support for broadcasting or storing typing indicator events.
- **Message Search**: There is no dedicated function or FTS (Full-Text Search) index on the `app_chat.message` table to allow users to search their conversation history.
- **Group Chat**: The `start_conversation` function explicitly blocks conversations with more than two members, and the `is_group` flag logic is a placeholder.

## Financial System (Soda & Caps)

- **Caps Earning Management**: The logic for earning Caps is partially implemented via triggers for specific actions (like reviews and orders), but there is no centralized system or admin interface to manage earning rules (e.g., for daily tasks, events).
- **Caps Marketplace**: The schema is missing tables to support a marketplace where users can spend Caps. This would require tables for `store_items`, `user_inventory`, and `purchase_log`.

## Engagement and Gamification

- **Daily Tasks**: No schema exists to define or track user progress on daily tasks. This would require `task` and `user_task_progress` tables.
- **Daily Login Bonuses**: The database does not have tables to manage login streaks or claimable daily bonuses. This would need `login_bonus_config` and `user_login_streak` tables.
- **Achievements and Badges**: There is no schema for defining achievements/badges or tracking which users have earned them. This would require `achievement` and `user_achievement` tables.
- **Leaderboards**: There are no dedicated views or tables for generating leaderboards. While the `app_provider.performance` table exists, it's not structured as a leaderboard and there's no equivalent for clients.

## Future Potential Features

The following planned features have no corresponding database schema:

- **Phone Verification**
- **Subscription Tiers**
- **Gift Giving** (Soda or Caps between users)
- **Affiliate Program**
- **Voice/Video Calls**
