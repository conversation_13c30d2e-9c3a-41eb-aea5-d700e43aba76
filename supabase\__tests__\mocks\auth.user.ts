import { SupabaseClient, User } from "@supabase/supabase-js";
import { Database } from "shared/lib/supabase/database";
import { createClient } from "@supabase/supabase-js";
import { afterAll, beforeAll, expect } from "vitest";
import { randomUUID } from "node:crypto";
import { serviceClient } from "../utils/client";

export type MockUser = {
  client?: SupabaseClient<Database>;
  data?: User;
};

export type MockUserOptions = {
  role: string;
  soda_balance?: number;
  cap_balance?: number;
};

export async function createAndSignInUser(role: string): Promise<MockUser> {
  const userId = randomUUID();

  const userCreation = await serviceClient.auth.admin.createUser({
    id: userId,
    email: `${role}-${userId}@example.com`,
    password: "password123",
    email_confirm: true
  });

  if (!userCreation.data.user) {
    throw `Failed to create ${role} user`;
  }

  // assign role to user
  await serviceClient.schema("app_access").rpc("assign_role_to_user", {
    v_user_id: userCreation.data.user.id,
    v_role_name: role
  });

  const client = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  await client.auth.signInWithPassword({
    email: userCreation.data.user.email ?? "",
    password: "password123"
  });

  return { client, data: userCreation.data.user };
}

export function mockUser(options: MockUserOptions) {
  const { role, soda_balance = 0, cap_balance = 0 } = options;
  const user: MockUser = {};

  beforeAll(async () => {
    const { client, data } = await createAndSignInUser(role);

    user.client = client;
    user.data = data;

    if (user.data) {
      await serviceClient
        .schema("app_transaction")
        .from("wallet")
        .upsert({ user_id: user.data.id, soda_balance, cap_balance })
        .select();

      await serviceClient
        .schema("app_account")
        .from("profile")
        .upsert({
          user_id: user.data.id,
          username: `testuser-${user.data.id.slice(0, 8)}`
        })
        .select();
    }
  });

  afterAll(async () => {
    if (user.data) {
      await serviceClient.auth.admin.deleteUser(user.data.id);
    }
  });

  return user;
}

export type MockCustomerOptions = {
  soda_balance?: number;
  cap_balance?: number;
};

export type MockProviderOptions = {
  soda_balance?: number;
  cap_balance?: number;
};

export function mockAdmin() {
  return mockUser({ role: "admin" });
}

export function mockCustomer(options: MockCustomerOptions = {}) {
  const { soda_balance = 1000, cap_balance = 0 } = options;

  const customer = mockUser({ role: "customer", soda_balance, cap_balance });

  return customer;
}

export function mockProvider(options: MockProviderOptions = {}) {
  const { soda_balance = 0, cap_balance = 0 } = options;
  const provider = mockUser({ role: "customer", soda_balance, cap_balance });

  beforeAll(async () => {
    if (!provider.data) throw new Error("Provider data is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");

    const approveProvider = await serviceClient
      .schema("app_provider")
      .from("approved_user")
      .insert({ user_id: provider.data.id })
      .select()
      .single();

    expect(approveProvider.data?.user_id).toBe(provider.data.id);

    const setOpenForOrder = await provider.client
      .schema("app_provider")
      .from("status")
      .insert({
        user_id: provider.data.id,
        is_open_for_orders: true
      })
      .select()
      .single();

    expect(setOpenForOrder.data?.user_id).toBe(provider.data.id);
  });

  return provider;
}

export function mockProviderApplicant() {
  return mockUser({ role: "provider_applicant" });
}

export function mockSupportAgent() {
  return mockUser({ role: "support_agent" });
}
