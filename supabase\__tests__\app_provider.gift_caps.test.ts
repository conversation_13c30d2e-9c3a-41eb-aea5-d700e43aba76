import { test, expect, afterAll } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockCustomer, mockProvider } from "./mocks/auth.user";

createSetupHooks();

const customer = mockCustomer({ cap_balance: 1000 });
const provider = mockProvider({ cap_balance: 100 });
const anotherProvider = mockProvider({ cap_balance: 50 });

afterAll(async () => {
  // Clean up test data
  if (customer.data && provider.data && anotherProvider.data) {
    await serviceClient
      .schema("app_transaction")
      .from("transfer")
      .delete()
      .in("sender_id", [customer.data.id, provider.data.id])
      .in("receiver_id", [provider.data.id, anotherProvider.data.id]);
  }
});

test("should successfully gift caps from customer to provider", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  const giftAmount = 200;

  // Get initial balances
  const { data: initialCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: initialProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  const initialCustomerBalance = initialCustomerWallet?.cap_balance || 0;
  const initialProviderBalance = initialProviderWallet?.cap_balance || 0;

  // Gift caps to provider
  const { data: transferId, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: provider.data.id,
      p_cap_amount: giftAmount
    });

  expect(error).toBeNull();
  expect(transferId).toBeDefined();

  // Verify balances after transfer
  const { data: finalCustomerWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", customer.data.id)
    .single();

  const { data: finalProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  expect(finalCustomerWallet?.cap_balance).toBe(
    initialCustomerBalance - giftAmount
  );
  expect(finalProviderWallet?.cap_balance).toBe(
    initialProviderBalance + giftAmount
  );

  if (!transferId) throw new Error("Transfer ID is undefined");

  // Verify transfer record was created
  const { data: transfer } = await serviceClient
    .schema("app_transaction")
    .from("transfer")
    .select("*")
    .eq("id", transferId)
    .single();

  expect(transfer?.sender_id).toBe(customer.data.id);
  expect(transfer?.receiver_id).toBe(provider.data.id);
  expect(transfer?.cap_amount).toBe(giftAmount);
  expect(transfer?.soda_amount).toBe(0);
});

test("should fail when gifting caps with insufficient balance", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  const excessiveAmount = 10000; // More than customer's balance

  const { data, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: provider.data.id,
      p_cap_amount: excessiveAmount
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Insufficient cap balance");
  expect(data).toBeNull();
});

test("should fail when gifting to non-existent or non-approved provider", async () => {
  if (!customer.client || !customer.data) {
    throw new Error("Customer not defined");
  }

  // Use a random UUID for a non-existent user
  const nonExistentUserId = "00000000-0000-0000-0000-000000000000";

  const { data, error } = await customer.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: nonExistentUserId,
      p_cap_amount: 100
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Provider not found or not approved");
  expect(data).toBeNull();
});

test("should fail when gifting zero or negative amount", async () => {
  if (!customer.client || !customer.data || !provider.data) {
    throw new Error("Customer or provider not defined");
  }

  // Test zero amount
  const { data: data1, error: error1 } = await customer.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: provider.data.id,
      p_cap_amount: 0
    });

  expect(error1).not.toBeNull();
  expect(error1?.message).toContain("Cap amount must be greater than zero");
  expect(data1).toBeNull();

  // Test negative amount (domain constraint will catch this)
  const { data: data2, error: error2 } = await customer.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: provider.data.id,
      p_cap_amount: -50
    });

  expect(error2).not.toBeNull();
  expect(error2?.message).toContain("token_unit_check");
  expect(data2).toBeNull();
});

test("should fail when trying to gift caps to yourself", async () => {
  if (!provider.client || !provider.data) {
    throw new Error("Provider not defined");
  }

  const { data, error } = await provider.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: provider.data.id,
      p_cap_amount: 50
    });

  expect(error).not.toBeNull();
  expect(error?.message).toContain("Cannot gift caps to yourself");
  expect(data).toBeNull();
});

test("should allow provider to gift caps to another provider", async () => {
  if (!provider.client || !provider.data || !anotherProvider.data) {
    throw new Error("Provider or another provider not defined");
  }

  const giftAmount = 25;

  // Get initial balances
  const { data: initialProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  const { data: initialAnotherProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", anotherProvider.data.id)
    .single();

  const initialProviderBalance = initialProviderWallet?.cap_balance || 0;
  const initialAnotherProviderBalance =
    initialAnotherProviderWallet?.cap_balance || 0;

  // Gift caps from provider to another provider
  const { data: transferId, error } = await provider.client
    .schema("app_provider")
    .rpc("gift_caps_to_provider", {
      p_provider_id: anotherProvider.data.id,
      p_cap_amount: giftAmount
    });

  expect(error).toBeNull();
  expect(transferId).toBeDefined();

  // Verify balances after transfer
  const { data: finalProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", provider.data.id)
    .single();

  const { data: finalAnotherProviderWallet } = await serviceClient
    .schema("app_transaction")
    .from("wallet")
    .select("cap_balance")
    .eq("user_id", anotherProvider.data.id)
    .single();

  expect(finalProviderWallet?.cap_balance).toBe(
    initialProviderBalance - giftAmount
  );
  expect(finalAnotherProviderWallet?.cap_balance).toBe(
    initialAnotherProviderBalance + giftAmount
  );
});
