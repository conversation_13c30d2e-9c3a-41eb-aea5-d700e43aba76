import { mockConversation } from "./mocks/app_chat.conversation";
import { mockConversationPreference } from "./mocks/app_chat.conversation_preference";
import { mockLog } from "./mocks/app_chat.log";
import { mockMessage } from "./mocks/app_chat.message";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";
import { beforeAll, describe, expect, test } from "vitest";

createSetupHooks();

describe("start_conversation", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  const provider1 = mockProvider();
  const provider2 = mockProvider();

  const conversation1 = mockConversation({
    members: [customer1, provider1]
  });
  const conversation2 = mockConversation({
    members: [customer1, customer2]
  });

  beforeAll(async () => {
    if (!customer1.client) throw new Error("Customer client is undefined");
    if (!provider1.client) throw new Error("Provider client is undefined");
    if (!provider2.data) throw new Error("Provider data is undefined");
    if (!customer2.data) throw new Error("Customer data is undefined");

    await customer1.client.schema("app_account").from("user_block").insert({
      blocked_id: provider2.data.id
    });

    await provider1.client.schema("app_account").from("user_block").insert({
      blocked_id: customer2.data.id
    });
  });

  const blockedConversation1 = mockConversation({
    members: [customer1, provider2]
  });
  const blockedConversation2 = mockConversation({
    members: [provider1, customer2]
  });

  test("customer can start conversation with provider", async () => {
    expect(conversation1.id).toBeDefined();
  });

  test("customer cannot start conversation with another customer", async () => {
    expect(conversation2.id).toBeUndefined();
  });

  test("customer or provider cannot start conversation with blocked user by them", () => {
    expect(blockedConversation1.id).toBeUndefined();
    expect(blockedConversation2.id).toBeUndefined();
  });
});

describe("user", () => {
  const customer = mockCustomer();
  const provider1 = mockProvider();
  const provider2 = mockProvider();

  const conversation = mockConversation({
    members: [customer, provider1]
  });

  const customerMessage = mockMessage({
    sender: customer,
    conversation: conversation,
    content: "Test message"
  });

  const providerMessage = mockMessage({
    sender: provider1,
    conversation: conversation,
    content: "Test message"
  });

  const conversation2 = mockConversation({
    members: [customer, provider2]
  });

  const customerMessageToDelete = mockMessage({
    sender: customer,
    conversation: conversation2,
    content: "Test message"
  });

  mockConversationPreference({
    user: customer,
    conversation: conversation
  });

  mockConversationPreference({
    user: provider1,
    conversation: conversation
  });

  test("can edit its own message", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customerMessage.id)
      throw new Error("Customer message ID is undefined");

    await customer.client
      .schema("app_chat")
      .from("message")
      .update({ content: "Updated message" })
      .eq("id", customerMessage.id);

    const { data: checkMessage } = await customer.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("id", customerMessage.id)
      .single();

    expect(checkMessage?.content).toBe("Updated message");
  });

  test("can delete its own message", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customerMessageToDelete.id)
      throw new Error("Customer message ID is undefined");

    await customer.client
      .schema("app_chat")
      .from("message")
      .delete()
      .eq("id", customerMessageToDelete.id);

    const checkIfMessageDeleted = await customer.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("id", customerMessageToDelete.id)
      .single();

    expect(checkIfMessageDeleted.data).toBeNull();
  });

  test("cannot delete other's message", async () => {
    if (!customer.client) throw new Error("Provider client is undefined");
    if (!providerMessage.id)
      throw new Error("Provider message ID is undefined");

    await customer.client
      .schema("app_chat")
      .from("message")
      .delete()
      .eq("id", providerMessage.id);

    const checkIfMessageDeleted = await customer.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("id", providerMessage.id)
      .single();

    expect(checkIfMessageDeleted.data?.id).toBe(providerMessage.id);
  });

  test("cannot see messages in blocked conversation", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider1.client) throw new Error("Provider client is undefined");
    if (!conversation.id) throw new Error("Conversation ID is undefined");
    if (!provider1.data) throw new Error("Provider data is undefined");

    const messages1 = await customer.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("conversation_id", conversation.id);

    expect(messages1.data).toHaveLength(2);

    const messages2 = await provider1.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("conversation_id", conversation.id);

    expect(messages2.data).toHaveLength(2);

    await customer.client.schema("app_account").from("user_block").insert({
      blocked_id: provider1.data.id
    });

    const checkMessages1 = await customer.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("conversation_id", conversation.id);

    expect(checkMessages1.data).toHaveLength(0);

    const checkMessages2 = await provider1.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("conversation_id", conversation.id);

    expect(checkMessages2.data).toHaveLength(0);
  });

  test("can only see conversation preferences related to itself", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider1.data) throw new Error("Provider data is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!conversation.id) throw new Error("Conversation ID is undefined");

    const viewPreferences = await customer.client
      .schema("app_chat")
      .from("conversation_preference")
      .select("*")
      .eq("user_id", customer.data?.id)
      .eq("conversation_id", conversation.id);

    expect(viewPreferences.data?.length).toBe(1);
  });
});

describe("logs", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const conversation = mockConversation({
    members: [customer, provider]
  });

  mockLog({
    user: customer,
    conversation: conversation,
    action: "test_action"
  });

  mockLog({
    user: provider,
    conversation: conversation,
    action: "test_action"
  });

  test("user can only see log related to its user id", async () => {
    if (!conversation.id) throw new Error("Conversation ID is undefined");
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider.client) throw new Error("Provider client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    const viewLogs1 = await customer.client
      .schema("app_chat")
      .from("log")
      .select("*")
      .eq("user_id", customer.data?.id)
      .eq("conversation_id", conversation.id);

    expect(viewLogs1.data?.length).toBe(1);

    const viewLogs2 = await provider.client
      .schema("app_chat")
      .from("log")
      .select("*")
      .eq("user_id", provider.data?.id)
      .eq("conversation_id", conversation.id);

    expect(viewLogs2.data?.length).toBe(1);
  });
});

describe("admin", () => {
  const admin = mockAdmin();
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  const provider = mockProvider();

  const conversation = mockConversation({
    members: [customer1, provider]
  });

  const message = mockMessage({
    sender: customer1,
    conversation: conversation,
    content: "Test message"
  });

  const log = mockLog({
    user: customer1,
    conversation: conversation,
    action: "test_action"
  });

  const conversation2 = mockConversation({
    members: [customer2, provider]
  });

  mockConversationPreference({
    user: customer1,
    conversation: conversation
  });

  mockConversationPreference({
    user: customer2,
    conversation: conversation2
  });

  mockConversationPreference({
    user: provider,
    conversation: conversation2
  });

  test("can view all conversations, messages (deleted ones too), preferences, and logs", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!message.id) throw new Error("Message ID is undefined");
    if (!log.id) throw new Error("Log ID is undefined");

    const viewConversations = await admin.client
      .schema("app_chat")
      .from("conversation")
      .select("*");

    expect(viewConversations.data?.map((c) => c.id)).toContain(conversation.id);
    expect(viewConversations.data?.map((c) => c.id)).toContain(
      conversation2.id
    );

    const viewMessage = await admin.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("id", message.id)
      .single();

    expect(viewMessage.data?.id).toBe(message.id);

    const viewLog = await admin.client
      .schema("app_chat")
      .from("log")
      .select("*")
      .eq("id", log.id)
      .single();

    expect(viewLog.data?.id).toBe(log.id);

    await admin.client
      .schema("app_chat")
      .from("message")
      .delete()
      .eq("id", message.id);

    const checkIfMessageDeleted = await admin.client
      .schema("app_chat")
      .from("message")
      .select("*")
      .eq("id", message.id)
      .single();

    expect(checkIfMessageDeleted.data).toBeNull();

    const viewDeletedMessage = await admin.client
      .schema("app_chat")
      .from("deleted_message")
      .select("*")
      .eq("id", message.id)
      .single();

    expect(viewDeletedMessage.data?.id).toBe(message.id);

    const viewPreferences = await admin.client
      .schema("app_chat")
      .from("conversation_preference")
      .select("*");

    expect(viewPreferences.data?.length).toBe(3);
  });
});
