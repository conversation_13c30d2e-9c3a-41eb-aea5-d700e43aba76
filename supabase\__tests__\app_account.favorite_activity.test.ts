import { describe, test, expect, afterAll } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";

createSetupHooks();

describe("Favorite Activity Functionality", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  const provider = mockProvider();
  const admin = mockAdmin();
  const activity1 = mockCatalogActivity({ admin });
  const activity2 = mockCatalogActivity({ admin });

  afterAll(async () => {
    // Clean up all favorite activity records
    await serviceClient
      .schema("app_account")
      .from("favorite_activity")
      .delete()
      .neq("user_id", "********-0000-0000-0000-************");
  });

  test("user can add an activity to favorites", async () => {
    if (!customer1.client || !customer1.data || !activity1.id)
      throw new Error("Customer or activity not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    expect(error).toBeNull();

    // Verify the favorite was added
    const { data: favorites } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(favorites).toHaveLength(1);
    expect(favorites?.[0]?.user_id).toBe(customer1.data.id);
    expect(favorites?.[0]?.activity_id).toBe(activity1.id);
  });

  test("user can remove an activity from favorites", async () => {
    if (!customer1.client || !customer1.data || !activity1.id)
      throw new Error("Customer or activity not defined");

    // First add to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Then remove from favorites
    const { error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .delete()
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(error).toBeNull();

    // Verify the favorite was removed
    const { data: favorites } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(favorites).toHaveLength(0);
  });

  test("user can view their own favorite activities", async () => {
    if (!customer1.client || !customer1.data || !activity1.id || !activity2.id)
      throw new Error("Customer or activities not defined");

    // Add multiple activities to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert([
        {
          user_id: customer1.data.id,
          activity_id: activity1.id
        },
        {
          user_id: customer1.data.id,
          activity_id: activity2.id
        }
      ]);

    // View own favorites
    const { data: favorites, error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id);

    expect(error).toBeNull();
    expect(favorites?.length).toBeGreaterThanOrEqual(2);
    expect(favorites?.some((f) => f.activity_id === activity1.id)).toBe(true);
    expect(favorites?.some((f) => f.activity_id === activity2.id)).toBe(true);
  });

  test("customer cannot view other users' favorite activities", async () => {
    if (
      !customer1.client ||
      !customer2.client ||
      !customer1.data ||
      !customer2.data ||
      !activity1.id
    )
      throw new Error("Customers or activity not defined");

    // Customer2 adds activity to favorites
    await customer2.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer2.data.id,
        activity_id: activity1.id
      });

    // Customer1 tries to view Customer2's favorites
    const { data: favorites } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer2.data.id);

    // Should return empty due to RLS
    expect(favorites).toHaveLength(0);
  });

  test("provider can view other users' favorite activities", async () => {
    if (
      !provider.client ||
      !customer1.client ||
      !customer1.data ||
      !activity1.id
    )
      throw new Error("Provider, customer, or activity not defined");

    // Customer adds activity to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Provider tries to view Customer's favorites
    const { data: favorites, error } = await provider.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id);

    // Provider should be able to see customer's favorites
    expect(error).toBeNull();
    expect(favorites?.length).toBeGreaterThanOrEqual(1);
    expect(
      favorites?.some(
        (f) =>
          f.activity_id === activity1.id && f.user_id === customer1.data?.id
      )
    ).toBe(true);
  });

  test("provider can view all favorite activities", async () => {
    if (
      !provider.client ||
      !customer1.client ||
      !customer2.client ||
      !customer1.data ||
      !customer2.data ||
      !activity1.id ||
      !activity2.id
    )
      throw new Error("Provider, customers, or activities not defined");

    // Customer1 adds activity1 to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Customer2 adds activity2 to favorites
    await customer2.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer2.data.id,
        activity_id: activity2.id
      });

    // Provider can view all favorites without filtering by user
    const { data: allFavorites, error } = await provider.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*");

    // Provider should be able to see all favorites
    expect(error).toBeNull();
    expect(allFavorites?.length).toBeGreaterThanOrEqual(2);
    expect(
      allFavorites?.some(
        (f) =>
          f.activity_id === activity1.id && f.user_id === customer1.data?.id
      )
    ).toBe(true);
    expect(
      allFavorites?.some(
        (f) =>
          f.activity_id === activity2.id && f.user_id === customer2.data?.id
      )
    ).toBe(true);
  });

  test("user cannot add favorites for other users", async () => {
    if (!customer1.client || !customer2.data || !activity1.id)
      throw new Error("Customer or activity not defined");

    // Customer1 tries to add favorite for Customer2
    const { error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer2.data.id,
        activity_id: activity1.id
      });

    // Should fail due to RLS policy
    expect(error).not.toBeNull();
  });

  test("user cannot remove favorites for other users", async () => {
    if (
      !customer1.client ||
      !customer2.client ||
      !customer2.data ||
      !activity1.id
    )
      throw new Error("Customers or activity not defined");

    // Customer2 adds activity to favorites
    await customer2.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer2.data.id,
        activity_id: activity1.id
      });

    // Customer1 tries to remove Customer2's favorite
    const { error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .delete()
      .eq("user_id", customer2.data.id)
      .eq("activity_id", activity1.id);

    expect(error).toBeNull(); // DELETE operations don't fail but affect 0 rows

    // Verify the favorite still exists
    const { data: favorites } = await customer2.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer2.data.id)
      .eq("activity_id", activity1.id);

    expect(favorites).toHaveLength(1);
  });

  test("duplicate favorites are prevented by primary key constraint", async () => {
    if (!customer1.client || !customer1.data || !activity1.id)
      throw new Error("Customer or activity not defined");

    // Add activity to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Try to add the same activity again
    const { error } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Should fail due to primary key constraint
    expect(error).not.toBeNull();
    expect(error?.code).toBe("23505"); // unique_violation
  });

  test("favorite is automatically removed when activity is deleted", async () => {
    if (!customer1.client || !customer1.data || !admin.client)
      throw new Error("Customer or admin not defined");

    // Create a temporary activity
    const { data: tempActivity } = await admin.client
      .schema("app_catalog")
      .from("activity")
      .insert({
        name: { en: "Temporary Activity" }
      })
      .select()
      .single();

    if (!tempActivity?.id)
      throw new Error("Failed to create temporary activity");

    // Add activity to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: tempActivity.id
      });

    // Verify favorite exists
    const { data: favoritesBefore } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", tempActivity.id);

    expect(favoritesBefore).toHaveLength(1);

    // Delete the activity
    await admin.client
      .schema("app_catalog")
      .from("activity")
      .delete()
      .eq("id", tempActivity.id);

    // Verify favorite is automatically removed due to CASCADE
    const { data: favoritesAfter } = await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", tempActivity.id);

    expect(favoritesAfter).toHaveLength(0);
  });

  test("admin can view all favorite activities", async () => {
    if (!admin.client || !customer1.client || !customer1.data || !activity1.id)
      throw new Error("Admin, customer, or activity not defined");

    // Customer adds activity to favorites
    await customer1.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    // Admin can view all favorites
    const { data: allFavorites, error } = await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*");

    expect(error).toBeNull();
    expect(allFavorites?.length).toBeGreaterThan(0);
    expect(
      allFavorites?.some(
        (f) =>
          f.user_id === customer1.data?.id && f.activity_id === activity1.id
      )
    ).toBe(true);
  });

  test("admin can manage any user's favorite activities", async () => {
    if (!admin.client || !customer1.data || !activity1.id)
      throw new Error("Admin, customer, or activity not defined");

    // Clean up any existing favorite first
    await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .delete()
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    // Admin adds favorite for customer
    const { error: insertError } = await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .insert({
        user_id: customer1.data.id,
        activity_id: activity1.id
      });

    expect(insertError).toBeNull();

    // Verify favorite was added
    const { data: favorites } = await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(favorites).toHaveLength(1);

    // Admin removes favorite for customer
    const { error: deleteError } = await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .delete()
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(deleteError).toBeNull();

    // Verify favorite was removed
    const { data: favoritesAfter } = await admin.client
      .schema("app_account")
      .from("favorite_activity")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("activity_id", activity1.id);

    expect(favoritesAfter).toHaveLength(0);
  });
});
