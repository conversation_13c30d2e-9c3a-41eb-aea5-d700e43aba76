import { describe, test, expect, afterAll } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";

createSetupHooks();

describe("Profile Visit Functionality", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  const provider = mockProvider();
  const admin = mockAdmin();

  afterAll(async () => {
    // Clean up all visit records
    await serviceClient
      .schema("app_account")
      .from("visit")
      .delete()
      .neq("visitor_id", "********-0000-0000-0000-************");
  });

  test("user can record a profile visit", async () => {
    if (!customer1.client || !customer1.data || !customer2.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .rpc("record_profile_visit", {
        p_visited_id: customer2.data.id
      });

    expect(error).toBeNull();

    // Verify the visit was recorded
    const { data: visits } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer1.data.id)
      .eq("visited_id", customer2.data.id);

    expect(visits).toHaveLength(1);
    expect(visits?.[0]?.visitor_id).toBe(customer1.data.id);
    expect(visits?.[0]?.visited_id).toBe(customer2.data.id);
  });

  test("user cannot record self-visit", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .rpc("record_profile_visit", {
        p_visited_id: customer1.data.id
      });

    expect(error).toBeNull(); // Function returns without error but doesn't record

    // Verify no self-visit was recorded
    const { data: visits } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer1.data.id)
      .eq("visited_id", customer1.data.id);

    expect(visits).toHaveLength(0);
  });

  test("visit record is updated on subsequent visits", async () => {
    if (!customer1.client || !customer1.data || !provider.data)
      throw new Error("Customer/Provider not defined");

    // First visit
    const { error: firstError } = await customer1.client
      .schema("app_account")
      .rpc("record_profile_visit", {
        p_visited_id: provider.data.id
      });

    expect(firstError).toBeNull();

    // Get the first visit timestamp
    const { data: firstVisit } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("visited_at")
      .eq("visitor_id", customer1.data.id)
      .eq("visited_id", provider.data.id)
      .single();

    expect(firstVisit).toBeTruthy();

    // Wait a moment and visit again
    await new Promise((resolve) => setTimeout(resolve, 100));

    const { error: secondError } = await customer1.client
      .schema("app_account")
      .rpc("record_profile_visit", {
        p_visited_id: provider.data.id
      });

    expect(secondError).toBeNull();

    // Verify only one record exists but timestamp is updated
    const { data: visits } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer1.data.id)
      .eq("visited_id", provider.data.id);

    expect(visits).toHaveLength(1);
    expect(new Date(visits?.[0]?.visited_at ?? "").getTime()).toBeGreaterThan(
      new Date(firstVisit?.visited_at ?? "").getTime()
    );
  });

  test("user can view their own visit history", async () => {
    if (
      !customer1.client ||
      !customer1.data ||
      !customer2.data ||
      !provider.data
    )
      throw new Error("Users not defined");

    // Record visits to multiple users
    await customer1.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: customer2.data.id
    });

    await customer1.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: provider.data.id
    });

    // View own visit history
    const { data: visits, error } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer1.data.id);

    expect(error).toBeNull();
    expect(visits?.length).toBeGreaterThanOrEqual(2);
  });

  test("user can view who visited their profile", async () => {
    if (
      !customer1.client ||
      !customer1.data ||
      !customer2.client ||
      !customer2.data
    )
      throw new Error("Customers not defined");

    // Customer2 visits Customer1's profile
    await customer2.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: customer1.data.id
    });

    // Customer1 can see who visited their profile
    const { data: visitors, error } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visited_id", customer1.data.id);

    expect(error).toBeNull();
    expect(visitors?.some((v) => v.visitor_id === customer2.data?.id)).toBe(
      true
    );
  });

  test("user cannot view other users' visit history", async () => {
    if (
      !customer1.client ||
      !customer2.client ||
      !customer1.data ||
      !customer2.data ||
      !provider.data
    )
      throw new Error("Users not defined");

    // Clean up any existing visits first
    await serviceClient
      .schema("app_account")
      .from("visit")
      .delete()
      .eq("visitor_id", customer2.data.id);

    // Customer2 visits provider's profile
    await customer2.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: provider.data.id
    });

    // Customer1 tries to view Customer2's visit history (where Customer2 is the visitor)
    const { data: visits } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer2.data.id);

    // Should return empty due to RLS - Customer1 cannot see Customer2's visits
    expect(visits).toHaveLength(0);
  });

  test("admin can view all visit records", async () => {
    if (!admin.client) throw new Error("Admin client is undefined");
    if (!customer1.client) throw new Error("Customer client is undefined");
    if (!customer2.data) throw new Error("Customer data is undefined");

    // Record a visit
    await customer1.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: customer2.data.id
    });

    // Admin can view all visits
    const { data: allVisits, error } = await admin.client
      .schema("app_account")
      .from("visit")
      .select("*");

    expect(error).toBeNull();
    expect(allVisits?.length).toBeGreaterThan(0);
  });

  test("function rejects non-existent visited user", async () => {
    if (!customer1.client) throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_account")
      .rpc("record_profile_visit", {
        p_visited_id: "********-0000-0000-0000-************"
      });

    expect(error).toBeTruthy();
    expect(error?.message).toContain("Visited user does not exist");
  });

  test("old visit records are cleaned up", async () => {
    if (!customer1.client || !customer1.data || !customer2.data)
      throw new Error("Customers not defined");

    // Insert an old visit record directly (simulating a visit from 2 days ago)
    await serviceClient
      .schema("app_account")
      .from("visit")
      .insert({
        visitor_id: customer1.data.id,
        visited_id: customer2.data.id,
        visited_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
      });

    // Record a new visit (this should trigger cleanup)
    await customer1.client.schema("app_account").rpc("record_profile_visit", {
      p_visited_id: customer2.data.id
    });

    // Check that only the recent visit remains
    const { data: visits } = await customer1.client
      .schema("app_account")
      .from("visit")
      .select("*")
      .eq("visitor_id", customer1.data.id);

    // Should only have recent visits (within 1 day)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentVisits = visits?.filter(
      (v) => new Date(v.visited_at ?? "") > oneDayAgo
    );

    expect(recentVisits?.length).toBe(visits?.length);
  });
});
