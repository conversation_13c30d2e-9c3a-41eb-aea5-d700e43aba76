-- section SCHEMA
DROP SCHEMA IF EXISTS app_webapp CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_webapp
;

GRANT USAGE ON SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_webapp TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_webapp
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section MATERIALIZED VIEWS
-- anchor caps_leaderboard
CREATE MATERIALIZED VIEW app_webapp.caps_leaderboard AS
SELECT
  ROW_NUMBER() OVER (
    ORDER BY
      w.cap_balance DESC,
      p.join_date ASC
  ) AS rank,
  w.user_id,
  w.cap_balance,
  p.username,
  p.nickname,
  p.join_date
FROM
  app_transaction.wallet w
  INNER JOIN app_account.profile p ON w.user_id = p.user_id
  LEFT JOIN app_account.privacy pr ON w.user_id = pr.user_id
WHERE
  w.cap_balance > 0
  AND COALESCE(pr.show_in_leaderboard, TRUE) = TRUE
ORDER BY
  w.cap_balance DESC,
  p.join_date ASC
LIMIT
  20
;

-- Create index for better performance
CREATE UNIQUE INDEX caps_leaderboard_user_id_idx ON app_webapp.caps_leaderboard (user_id)
;

-- !section
-- section FUNCTIONS
-- anchor refresh_caps_leaderboard
CREATE OR REPLACE FUNCTION app_webapp.refresh_caps_leaderboard () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY app_webapp.caps_leaderboard;
END;
$$
;

-- anchor get_user_caps_rank
CREATE OR REPLACE FUNCTION app_webapp.get_user_caps_rank (
  p_user_id UUID DEFAULT auth.uid ()
) RETURNS TABLE (
  rank BIGINT,
  cap_balance app_transaction.TOKEN_UNIT,
  total_users BIGINT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    cl.rank,
    cl.cap_balance,
    (SELECT COUNT(*) FROM app_webapp.caps_leaderboard) as total_users
  FROM app_webapp.caps_leaderboard cl
  WHERE cl.user_id = p_user_id;
END;
$$
;

-- !section
-- section RLS POLICIES
-- anchor caps_leaderboard
ALTER MATERIALIZED VIEW app_webapp.caps_leaderboard OWNER TO postgres
;

-- Grant select permissions
GRANT
SELECT
  ON app_webapp.caps_leaderboard TO anon,
  authenticated,
  service_role
;

-- !section
-- section CRONJOBS
-- anchor Refresh Caps Leaderboard
SELECT
  cron.schedule (
    'Refresh Caps Leaderboard',
    '0 */1 * * *', -- Run once every hour
    'SELECT app_webapp.refresh_caps_leaderboard();'
  )
;

-- !section