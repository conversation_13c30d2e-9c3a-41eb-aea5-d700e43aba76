import { test, expect, beforeAll, describe } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockCustomer, mockProvider } from "./mocks/auth.user";

createSetupHooks();

const customer = mockCustomer({ cap_balance: 1000 });
const provider = mockProvider({ cap_balance: 1000 });
const customer2 = mockCustomer({ cap_balance: 0 });

describe("Provider Question Submission", () => {
  test("customer can submit question to provider", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    const { data: questionId, error } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "What is your experience with this type of service?"
      });

    expect(error).toBeNull();
    expect(questionId).toBeDefined();

    if (!questionId) throw new Error("Question ID is undefined");

    // Verify question was created
    const { data: question } = await serviceClient
      .schema("app_provider")
      .from("question")
      .select("*")
      .eq("id", questionId)
      .single();

    expect(question?.asker_id).toBe(customer.data.id);
    expect(question?.provider_id).toBe(provider.data.id);
    expect(question?.question_text).toBe(
      "What is your experience with this type of service?"
    );
    expect(question?.cap_cost).toBeGreaterThan(0); // Should be set by config
  });

  test("deducts caps from customer wallet", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    // Get cap cost from config
    const { data: config } = await serviceClient
      .schema("app_provider")
      .from("config")
      .select("cap_cost_for_question_submission")
      .single();

    const capCost = config?.cap_cost_for_question_submission || 100;

    // Get initial balance
    const { data: initialWallet } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", customer.data.id)
      .single();

    const initialBalance = initialWallet?.cap_balance || 0;

    await customer.client.schema("app_provider").rpc("submit_question", {
      p_provider_id: provider.data.id,
      p_question_text: "Another question about your services?"
    });

    // Check final balance
    const { data: finalWallet } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", customer.data.id)
      .single();

    const finalBalance = finalWallet?.cap_balance || 0;
    expect(finalBalance).toBe(initialBalance - capCost);
  });

  test("fails with insufficient caps", async () => {
    if (!customer2.client || !customer2.data || !provider.data)
      throw new Error("Customer2 or provider not defined");

    const { error } = await customer2.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "This should fail due to insufficient caps"
      });

    expect(error).not.toBeNull();
    expect(error?.message).toContain("Insufficient cap balance");
  });

  test("fails when asking question to self", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const { error } = await provider.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "Can I ask myself a question?"
      });

    expect(error).not.toBeNull();
    expect(error?.message).toContain("You cannot ask questions to yourself");
  });

  test("fails with invalid question text length", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    // Too short
    const { error: shortError } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "Short"
      });

    expect(shortError).not.toBeNull();

    // Too long
    const longText = "a".repeat(1001);
    const { error: longError } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: longText
      });

    expect(longError).not.toBeNull();
  });
});

describe("Provider Question Answer Submission", () => {
  let questionId: string;

  beforeAll(async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    // Create a question for testing answers
    const { data } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "What are your working hours for this service?"
      });

    if (!data) throw new Error("Question ID is undefined");

    questionId = data;
  });

  test("provider can answer their question", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const { data: answerId, error } = await provider.client
      .schema("app_provider")
      .rpc("submit_question_answer", {
        p_question_id: questionId,
        p_answer_text:
          "I am available Monday to Friday from 9 AM to 6 PM for this service."
      });

    expect(error).toBeNull();
    expect(answerId).toBeDefined();

    if (!answerId) throw new Error("Answer ID is undefined");

    // Verify answer was created
    const { data: answer } = await serviceClient
      .schema("app_provider")
      .from("question_answer")
      .select("*")
      .eq("id", answerId)
      .single();

    expect(answer?.question_id).toBe(questionId);
    expect(answer?.provider_id).toBe(provider.data.id);
    expect(answer?.answer_text).toBe(
      "I am available Monday to Friday from 9 AM to 6 PM for this service."
    );
    expect(answer?.cap_reward).toBeGreaterThan(0); // Should be set by config
  });

  test("rewards caps to provider", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");
    if (!provider.client) throw new Error("Provider client is undefined");

    // Get cap reward from config
    const { data: config } = await serviceClient
      .schema("app_provider")
      .from("config")
      .select("cap_reward_for_question_answer")
      .single();

    const capReward = config?.cap_reward_for_question_answer || 100;

    // Create another question for testing cap rewards
    const { data: newQuestionId } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "Do you offer any discounts for bulk orders?"
      });

    if (!newQuestionId) throw new Error("Question ID is undefined");

    // Get initial provider balance
    const { data: initialWallet } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", provider.data.id)
      .single();

    const initialBalance = initialWallet?.cap_balance || 0;

    await provider.client.schema("app_provider").rpc("submit_question_answer", {
      p_question_id: newQuestionId,
      p_answer_text: "Yes, I offer 10% discount for orders of 5 or more units."
    });

    // Check final balance
    const { data: finalWallet } = await serviceClient
      .schema("app_transaction")
      .from("wallet")
      .select("cap_balance")
      .eq("user_id", provider.data.id)
      .single();

    const finalBalance = finalWallet?.cap_balance || 0;
    expect(finalBalance).toBe(initialBalance + capReward);
  });

  test("updates provider performance metrics", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");
    if (!provider.client) throw new Error("Provider client is undefined");

    // Get initial performance metrics
    const { data: initialPerformance } = await serviceClient
      .schema("app_provider")
      .from("performance")
      .select("answered_questions")
      .eq("user_id", provider.data.id)
      .single();

    const initialCount = initialPerformance?.answered_questions || 0;

    // Create and answer another question
    const { data: newQuestionId } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "What is your typical response time for orders?"
      });

    if (!newQuestionId) throw new Error("Question ID is undefined");

    await provider.client.schema("app_provider").rpc("submit_question_answer", {
      p_question_id: newQuestionId,
      p_answer_text:
        "I typically respond within 24 hours and complete orders within 3-5 business days."
    });

    // Check updated performance metrics
    const { data: finalPerformance } = await serviceClient
      .schema("app_provider")
      .from("performance")
      .select("answered_questions")
      .eq("user_id", provider.data.id)
      .single();

    const finalCount = finalPerformance?.answered_questions || 0;
    expect(finalCount).toBe(initialCount + 1);
  });

  test("fails when non-provider tries to answer", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    // Create a question
    const { data: newQuestionId, error: questionError } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "Can someone else answer this question?"
      });

    console.log(questionError);
    if (!newQuestionId) throw new Error("Question ID is undefined");

    // Try to answer as customer (not the provider)
    const { error } = await customer.client
      .schema("app_provider")
      .rpc("submit_question_answer", {
        p_question_id: newQuestionId,
        p_answer_text: "This should fail because I'm not the provider."
      });

    expect(error).not.toBeNull();
    expect(error?.message).toContain(
      "You do not have permission to answer questions"
    );
  });

  test("fails when question already answered", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    // Try to answer the same question again
    const { error } = await provider.client
      .schema("app_provider")
      .rpc("submit_question_answer", {
        p_question_id: questionId,
        p_answer_text: "This should fail because question is already answered."
      });

    expect(error).not.toBeNull();
    expect(error?.message).toContain("This question has already been answered");
  });

  test("fails with invalid answer text length", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");
    if (!provider.client) throw new Error("Provider client is undefined");

    // Create a question for testing invalid answer lengths
    const { data: newQuestionId } = await customer.client
      .schema("app_provider")
      .rpc("submit_question", {
        p_provider_id: provider.data.id,
        p_question_text: "What is your policy on revisions?"
      });

    if (!newQuestionId) throw new Error("Question ID is undefined");

    // Too short answer
    const { error: shortError } = await provider.client
      .schema("app_provider")
      .rpc("submit_question_answer", {
        p_question_id: newQuestionId,
        p_answer_text: "Short"
      });

    expect(shortError).not.toBeNull();

    // Too long answer
    const longText = "a".repeat(2001);
    const { error: longError } = await provider.client
      .schema("app_provider")
      .rpc("submit_question_answer", {
        p_question_id: newQuestionId,
        p_answer_text: longText
      });

    expect(longError).not.toBeNull();
  });
});

describe("Question and Answer Visibility", () => {
  test("customer can view their own questions", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not defined");

    const { data: questions, error } = await customer.client
      .schema("app_provider")
      .from("question")
      .select("*")
      .eq("asker_id", customer.data.id);

    expect(error).toBeNull();
    expect(questions).toBeDefined();
    expect(questions?.length).toBeGreaterThan(0);
  });

  test("provider can view questions directed to them", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const { data: questions, error } = await provider.client
      .schema("app_provider")
      .from("question")
      .select("*")
      .eq("provider_id", provider.data.id);

    expect(error).toBeNull();
    expect(questions).toBeDefined();
    expect(questions?.length).toBeGreaterThan(0);
  });

  test("customer can view answers to their questions", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not defined");

    const { data: answers, error } = await customer.client
      .schema("app_provider")
      .from("question_answer")
      .select(
        `
        *,
        question!inner(asker_id)
      `
      )
      .eq("question.asker_id", customer.data.id);

    expect(error).toBeNull();
    expect(answers).toBeDefined();
  });

  test("provider can view their own answers", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const { data: answers, error } = await provider.client
      .schema("app_provider")
      .from("question_answer")
      .select("*")
      .eq("provider_id", provider.data.id);

    expect(error).toBeNull();
    expect(answers).toBeDefined();
  });
});
