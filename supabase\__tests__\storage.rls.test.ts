/**
 * Storage RLS Policy Tests
 *
 * Comprehensive test suite for Row Level Security policies on Supabase Storage buckets.
 *
 * BUCKET NAMING PATTERNS:
 * - account_avatar: filename must be {user_id} (exact match)
 * - provider_gallery: filename must start with {user_id}_ (prefix pattern)
 * - provider_activity: filename must be {user_id}_{catalog_activity_id} (validated against existing activities)
 * - provider_voice: filename can be {user_id} OR {user_id}_{catalog_activity_id} (validated against existing activities)
 * - catalog_activity: filename must be {catalog_activity_id} (admin only, validated against catalog activities)
 *
 * MIME TYPE RESTRICTIONS (from config.toml):
 * - account_avatar, provider_gallery, provider_activity, catalog_activity: image/* only
 * - provider_voice: audio/* only
 *
 * ROLE CAPABILITIES TESTED:
 * - customer: can edit account_avatar only
 * - provider: can edit account_avatar, provider_gallery, provider_activity, provider_voice
 * - provider_applicant: same as provider
 * - admin: can edit all buckets with *.all.edit capabilities
 *
 * ACCESS PATTERNS TESTED:
 * - SELECT: All buckets allow public read access
 * - INSERT/UPDATE/DELETE: Require authentication and proper capabilities
 * - Users can only modify their own files (except admins)
 * - Cross-user access prevention
 * - Filename pattern validation
 * - Activity existence validation for provider_activity and provider_voice
 * - Catalog activity existence validation for catalog_activity
 *
 * TEST COVERAGE (40 tests):
 * - Basic CRUD operations for each bucket
 * - Filename pattern validation
 * - Cross-user access prevention
 * - Role-based capability testing
 * - Admin override capabilities
 * - Comprehensive update and delete operations
 */

import { describe, test, expect, afterAll } from "vitest";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";
import {
  mockCustomer,
  mockProvider,
  mockProviderApplicant,
  mockAdmin,
  MockUser
} from "./mocks/auth.user";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockProviderActivity } from "./mocks/app_provider.activity";
import { createSetupHooks } from "./utils/createSetupHooks";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();
const providerApplicant = mockProviderApplicant();
const admin = mockAdmin();
const catalogActivity = mockCatalogActivity({ admin });
mockProviderActivity({ provider, catalogActivity });
mockProviderActivity({
  provider: providerApplicant,
  catalogActivity
});

// Test files with proper content types
const testImageBuffer = readFileSync(
  "supabase/__tests__/objects/test-avatar.jpg"
);
const testVoiceBuffer = readFileSync(
  "supabase/__tests__/objects/test-voice.mp3"
);

// Helper function to upload with proper content type
const uploadWithContentType = async (
  client: MockUser["client"],
  bucket: string,
  filename: string,
  buffer: Buffer
) => {
  if (!client) throw new Error("Client is undefined");

  const fileType = await fileTypeFromBuffer(buffer);

  return client.storage.from(bucket).upload(filename, buffer, {
    contentType: fileType?.mime || "application/octet-stream",
    upsert: true
  });
};

afterAll(async () => {
  // Clean up uploaded files
  const buckets = [
    "account_avatar",
    "provider_gallery",
    "provider_activity",
    "provider_voice",
    "catalog_activity"
  ];

  for (const bucket of buckets) {
    try {
      if (customer.client) {
        await customer.client.storage
          .from(bucket)
          .remove([customer.data?.id || ""]);
      }
      if (provider.client && provider.data) {
        await provider.client.storage
          .from(bucket)
          .remove([
            provider.data.id,
            `${provider.data.id}_test`,
            `${provider.data.id}_gallery_1`,
            `${provider.data.id}_update_test`,
            `${provider.data.id}_delete_test`,
            `${provider.data.id}_update_gallery`,
            `${provider.data.id}_delete_gallery`,
            `${provider.data.id}_voice_delete`,
            `${provider.data.id}_${catalogActivity.id || ""}`
          ]);
      }
      if (providerApplicant.client && providerApplicant.data) {
        await providerApplicant.client.storage
          .from(bucket)
          .remove([
            providerApplicant.data.id,
            `${providerApplicant.data.id}_gallery_test`,
            `${providerApplicant.data.id}_${catalogActivity.id || ""}`
          ]);
      }
      if (admin.client && catalogActivity.id) {
        await admin.client.storage.from(bucket).remove([catalogActivity.id]);
      }
    } catch {
      // Ignore cleanup errors
    }
  }
});

describe("account_avatar bucket", () => {
  test("customer can upload with correct filename (user_id)", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not defined");

    const { error } = await uploadWithContentType(
      customer.client,
      "account_avatar",
      customer.data.id,
      testImageBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded by querying storage objects
    const { data: objects } = await customer.client.storage
      .from("account_avatar")
      .list("", { search: customer.data.id });

    expect(objects?.some((obj) => obj.name === customer.data?.id)).toBe(true);
  });

  test("customer cannot upload with incorrect filename", async () => {
    if (!customer.client || !customer.data)
      throw new Error("Customer not defined");

    const { error } = await uploadWithContentType(
      customer.client,
      "account_avatar",
      "wrong_filename",
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await customer.client.storage
      .from("account_avatar")
      .list("", { search: "wrong_filename" });

    expect(objects?.some((obj) => obj.name === "wrong_filename")).toBe(false);
  });

  test("admin can upload any filename", async () => {
    if (!admin.client || !customer.data)
      throw new Error("Admin or customer not defined");

    const { error } = await uploadWithContentType(
      admin.client,
      "account_avatar",
      customer.data.id,
      testImageBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await admin.client.storage
      .from("account_avatar")
      .list("", { search: customer.data.id });

    expect(objects?.some((obj) => obj.name === customer.data?.id)).toBe(true);
  });
});

describe("provider_gallery bucket", () => {
  test("provider can upload with correct filename pattern (user_id_*)", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const filename = `${provider.data.id}_gallery_1`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_gallery",
      filename,
      testImageBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_gallery")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("provider cannot upload with incorrect filename pattern", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const wrongFilename = "wrong_filename";
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_gallery",
      wrongFilename,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_gallery")
      .list("", { search: wrongFilename });

    expect(objects?.some((obj) => obj.name === wrongFilename)).toBe(false);
  });

  test("provider cannot upload with other user's prefix", async () => {
    if (!provider.client || !customer.data)
      throw new Error("Provider or customer not defined");

    const wrongFilename = `${customer.data.id}_gallery_1`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_gallery",
      wrongFilename,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_gallery")
      .list("", { search: wrongFilename });

    expect(objects?.some((obj) => obj.name === wrongFilename)).toBe(false);
  });
});

describe("provider_activity bucket", () => {
  test("provider can upload with correct filename (user_id_catalog_activity_id)", async () => {
    if (!provider.client || !provider.data || !catalogActivity.id) {
      throw new Error("Provider or catalog activity not defined");
    }

    // The filename should be user_id + catalog_activity_id (not provider_activity_id)
    const filename = `${provider.data.id}_${catalogActivity.id}`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_activity",
      filename,
      testImageBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_activity")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("provider cannot upload with non-existent catalog_activity_id", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const fakeCatalogActivityId = "00000000-0000-0000-0000-000000000000";
    const filename = `${provider.data.id}_${fakeCatalogActivityId}`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_activity",
      filename,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_activity")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(false);
  });

  test("provider cannot upload with incorrect filename pattern", async () => {
    if (!provider.client) throw new Error("Provider not defined");

    const wrongFilename = "wrong_format";
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_activity",
      wrongFilename,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_activity")
      .list("", { search: wrongFilename });

    expect(objects?.some((obj) => obj.name === wrongFilename)).toBe(false);
  });
});

describe("provider_voice bucket", () => {
  test("provider can upload with user_id filename", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const filename = provider.data.id;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_voice",
      filename,
      testVoiceBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_voice")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("provider can upload with user_id_catalog_activity_id filename", async () => {
    if (!provider.client || !provider.data || !catalogActivity.id) {
      throw new Error("Provider or catalog activity not defined");
    }

    // The filename should be user_id + catalog_activity_id (not provider_activity_id)
    const filename = `${provider.data.id}_${catalogActivity.id}`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_voice",
      filename,
      testVoiceBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_voice")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("provider cannot upload with non-existent catalog_activity_id", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const fakeCatalogActivityId = "00000000-0000-0000-0000-000000000000";
    const filename = `${provider.data.id}_${fakeCatalogActivityId}`;
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_voice",
      filename,
      testVoiceBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_voice")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(false);
  });

  test("provider cannot upload with incorrect filename", async () => {
    if (!provider.client) throw new Error("Provider not defined");

    const wrongFilename = "wrong_filename";
    const { error } = await uploadWithContentType(
      provider.client,
      "provider_voice",
      wrongFilename,
      testVoiceBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await provider.client.storage
      .from("provider_voice")
      .list("", { search: wrongFilename });

    expect(objects?.some((obj) => obj.name === wrongFilename)).toBe(false);
  });
});

describe("catalog_activity bucket", () => {
  test("admin can upload with correct activity_id filename", async () => {
    if (!admin.client || !catalogActivity.id)
      throw new Error("Admin or catalog activity not defined");

    const filename = catalogActivity.id;
    const { error } = await uploadWithContentType(
      admin.client,
      "catalog_activity",
      filename,
      testImageBuffer
    );

    expect(error).toBeNull();

    // Verify file was uploaded
    const { data: objects } = await admin.client.storage
      .from("catalog_activity")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("admin cannot upload with non-existent activity_id", async () => {
    if (!admin.client) throw new Error("Admin not defined");

    const fakeActivityId = "00000000-0000-0000-0000-000000000000";
    const { error } = await uploadWithContentType(
      admin.client,
      "catalog_activity",
      fakeActivityId,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded
    const { data: objects } = await admin.client.storage
      .from("catalog_activity")
      .list("", { search: fakeActivityId });

    expect(objects?.some((obj) => obj.name === fakeActivityId)).toBe(false);
  });

  test("provider cannot upload to catalog_activity bucket", async () => {
    if (!provider.client || !catalogActivity.id)
      throw new Error("Provider or catalog activity not defined");
    if (!admin.client) throw new Error("Admin not defined");

    const { error } = await uploadWithContentType(
      provider.client,
      "catalog_activity",
      catalogActivity.id,
      testImageBuffer
    );

    expect(error).not.toBeNull();

    // Verify file was not uploaded by checking if the upload actually failed
    // Since the error is not null, we don't need to check the file list
    // But let's verify anyway to be thorough
    const { data: objects } = await admin.client.storage
      .from("catalog_activity")
      .list("", { search: catalogActivity.id });

    // If the provider upload failed (which it should), the file shouldn't exist
    // unless the admin uploaded it in another test
    const providerUploadedFile = objects?.find(
      (obj) => obj.name === catalogActivity.id
    );
    if (providerUploadedFile) {
      // If file exists, it must have been uploaded by admin in another test
      // Let's clean it up and verify provider couldn't upload
      await admin.client.storage
        .from("catalog_activity")
        .remove([catalogActivity.id]);
    }
  });
});

describe("SELECT policies (public read access)", () => {
  test("anonymous users can read from all buckets", async () => {
    if (!admin.client) throw new Error("Admin not defined");

    // First upload a file as admin
    const testFilename = "test_public_read";
    await uploadWithContentType(
      admin.client,
      "account_avatar",
      testFilename,
      testImageBuffer
    );

    // Try to list files without authentication
    const { data: objects, error } = await admin.client.storage
      .from("account_avatar")
      .list();

    expect(error).toBeNull();
    expect(Array.isArray(objects)).toBe(true);

    // Clean up
    await admin.client.storage.from("account_avatar").remove([testFilename]);
  });
});

describe("UPDATE and DELETE operations", () => {
  test("provider can update their own files", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const filename = `${provider.data.id}_update_test`;

    // First upload
    await uploadWithContentType(
      provider.client,
      "provider_gallery",
      filename,
      testImageBuffer
    );

    // Then update
    const fileType = await fileTypeFromBuffer(testImageBuffer);
    const { error } = await provider.client.storage
      .from("provider_gallery")
      .update(filename, testImageBuffer, {
        contentType: fileType?.mime || "application/octet-stream",
        upsert: true
      });

    expect(error).toBeNull();

    // Verify file still exists
    const { data: objects } = await provider.client.storage
      .from("provider_gallery")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);
  });

  test("provider can delete their own files", async () => {
    if (!provider.client || !provider.data)
      throw new Error("Provider not defined");

    const filename = `${provider.data.id}_delete_test`;

    // First upload
    await uploadWithContentType(
      provider.client,
      "provider_gallery",
      filename,
      testImageBuffer
    );

    // Then delete
    const { error } = await provider.client.storage
      .from("provider_gallery")
      .remove([filename]);

    expect(error).toBeNull();

    // Verify file was deleted
    const { data: objects } = await provider.client.storage
      .from("provider_gallery")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(false);
  });

  test("provider cannot delete other users' files", async () => {
    if (!provider.client || !admin.client || !customer.data) {
      throw new Error("Provider, admin, or customer not defined");
    }

    const filename = customer.data.id; // Use customer's user_id as filename

    // Admin uploads file for customer
    await uploadWithContentType(
      admin.client,
      "account_avatar",
      filename,
      testImageBuffer
    );

    // Provider tries to delete it (should fail due to RLS)
    await provider.client.storage.from("account_avatar").remove([filename]);

    // The delete operation might not return an error but should have 0 affected rows
    // So we need to check if the file still exists to verify the operation was blocked
    const { data: objects } = await admin.client.storage
      .from("account_avatar")
      .list("", { search: filename });

    expect(objects?.some((obj) => obj.name === filename)).toBe(true);

    // Clean up
    await admin.client.storage.from("account_avatar").remove([filename]);
  });

  describe("Provider Applicant Role Tests", () => {
    test("provider applicant can upload to account_avatar", async () => {
      if (!providerApplicant.client || !providerApplicant.data) {
        throw new Error("Provider applicant not defined");
      }

      const { error } = await uploadWithContentType(
        providerApplicant.client,
        "account_avatar",
        providerApplicant.data.id,
        testImageBuffer
      );

      expect(error).toBeNull();

      // Verify file was uploaded
      const { data: objects } = await providerApplicant.client.storage
        .from("account_avatar")
        .list("", { search: providerApplicant.data.id });

      expect(
        objects?.some((obj) => obj.name === providerApplicant.data?.id)
      ).toBe(true);
    });

    test("provider applicant can upload to provider_gallery", async () => {
      if (!providerApplicant.client || !providerApplicant.data) {
        throw new Error("Provider applicant not defined");
      }

      const filename = `${providerApplicant.data.id}_gallery_test`;
      const { error } = await uploadWithContentType(
        providerApplicant.client,
        "provider_gallery",
        filename,
        testImageBuffer
      );

      expect(error).toBeNull();

      // Verify file was uploaded
      const { data: objects } = await providerApplicant.client.storage
        .from("provider_gallery")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(true);
    });

    test("provider applicant can upload to provider_activity with correct filename", async () => {
      if (
        !providerApplicant.client ||
        !providerApplicant.data ||
        !catalogActivity.id
      ) {
        throw new Error("Provider applicant or catalog activity not defined");
      }

      const filename = `${providerApplicant.data.id}_${catalogActivity.id}`;
      const { error } = await uploadWithContentType(
        providerApplicant.client,
        "provider_activity",
        filename,
        testImageBuffer
      );

      expect(error).toBeNull();

      // Verify file was uploaded
      const { data: objects } = await providerApplicant.client.storage
        .from("provider_activity")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(true);
    });

    test("provider applicant can upload to provider_voice", async () => {
      if (!providerApplicant.client || !providerApplicant.data) {
        throw new Error("Provider applicant not defined");
      }

      const filename = providerApplicant.data.id;
      const { error } = await uploadWithContentType(
        providerApplicant.client,
        "provider_voice",
        filename,
        testVoiceBuffer
      );

      expect(error).toBeNull();

      // Verify file was uploaded
      const { data: objects } = await providerApplicant.client.storage
        .from("provider_voice")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(true);
    });

    test("provider applicant cannot upload to catalog_activity", async () => {
      if (!providerApplicant.client || !catalogActivity.id) {
        throw new Error("Provider applicant or catalog activity not defined");
      }

      const { error } = await uploadWithContentType(
        providerApplicant.client,
        "catalog_activity",
        catalogActivity.id,
        testImageBuffer
      );

      expect(error).not.toBeNull();
    });
  });

  describe("Admin Capabilities Tests", () => {
    test("admin can upload to any bucket with any filename", async () => {
      if (!admin.client || !customer.data || !catalogActivity.id) {
        throw new Error("Admin, customer, or catalog activity not defined");
      }

      // Test regular image buckets with arbitrary filenames
      const regularImageBuckets = [
        "account_avatar",
        "provider_gallery",
        "provider_activity"
      ];
      for (const bucket of regularImageBuckets) {
        const filename = `admin_test_${bucket}`;
        const { error } = await uploadWithContentType(
          admin.client,
          bucket,
          filename,
          testImageBuffer
        );

        expect(error).toBeNull();

        // Verify file was uploaded
        const { data: objects } = await admin.client.storage
          .from(bucket)
          .list("", { search: filename });

        expect(objects?.some((obj) => obj.name === filename)).toBe(true);

        // Clean up
        await admin.client.storage.from(bucket).remove([filename]);
      }

      // Test catalog_activity bucket with valid catalog activity ID
      const { error: catalogError } = await uploadWithContentType(
        admin.client,
        "catalog_activity",
        catalogActivity.id,
        testImageBuffer
      );

      expect(catalogError).toBeNull();

      // Verify file was uploaded
      const { data: catalogObjects } = await admin.client.storage
        .from("catalog_activity")
        .list("", { search: catalogActivity.id });

      expect(
        catalogObjects?.some((obj) => obj.name === catalogActivity.id)
      ).toBe(true);

      // Clean up
      await admin.client.storage
        .from("catalog_activity")
        .remove([catalogActivity.id]);

      // Test voice bucket with voice file
      const voiceFilename = "admin_test_provider_voice";
      const { error: voiceError } = await uploadWithContentType(
        admin.client,
        "provider_voice",
        voiceFilename,
        testVoiceBuffer
      );

      expect(voiceError).toBeNull();

      // Verify file was uploaded
      const { data: voiceObjects } = await admin.client.storage
        .from("provider_voice")
        .list("", { search: voiceFilename });

      expect(voiceObjects?.some((obj) => obj.name === voiceFilename)).toBe(
        true
      );

      // Clean up
      await admin.client.storage.from("provider_voice").remove([voiceFilename]);
    });

    test("admin can update files in any bucket", async () => {
      if (!admin.client) throw new Error("Admin not defined");

      const filename = "admin_update_test";

      // Upload initial file
      await uploadWithContentType(
        admin.client,
        "account_avatar",
        filename,
        testImageBuffer
      );

      // Update the file
      const fileType = await fileTypeFromBuffer(testImageBuffer);
      const { error } = await admin.client.storage
        .from("account_avatar")
        .update(filename, testImageBuffer, {
          contentType: fileType?.mime || "application/octet-stream",
          upsert: true
        });

      expect(error).toBeNull();

      // Clean up
      await admin.client.storage.from("account_avatar").remove([filename]);
    });

    test("admin can delete files in any bucket", async () => {
      if (!admin.client) throw new Error("Admin not defined");

      const filename = "admin_delete_test";

      // Upload file
      await uploadWithContentType(
        admin.client,
        "account_avatar",
        filename,
        testImageBuffer
      );

      // Delete the file
      const { error } = await admin.client.storage
        .from("account_avatar")
        .remove([filename]);

      expect(error).toBeNull();

      // Verify file was deleted
      const { data: objects } = await admin.client.storage
        .from("account_avatar")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(false);
    });
  });

  describe("Cross-User Access Prevention", () => {
    test("customer cannot upload to provider buckets", async () => {
      if (!customer.client || !customer.data)
        throw new Error("Customer not defined");

      // Test image buckets with image files
      const imageBuckets = ["provider_gallery", "provider_activity"];
      for (const bucket of imageBuckets) {
        const filename = `${customer.data.id}_test`;
        const { error } = await uploadWithContentType(
          customer.client,
          bucket,
          filename,
          testImageBuffer
        );

        expect(error).not.toBeNull();
        // The error could be either RLS policy, capability-related, or MIME type
        expect(error?.message).toMatch(
          /(row-level security policy|Unauthorized|mime type)/
        );
      }

      // Test voice bucket with voice file
      const filename = `${customer.data.id}_test`;
      const { error } = await uploadWithContentType(
        customer.client,
        "provider_voice",
        filename,
        testVoiceBuffer
      );

      expect(error).not.toBeNull();
      expect(error?.message).toMatch(
        /(row-level security policy|Unauthorized|mime type)/
      );
    });

    test("provider cannot upload to other provider's gallery files", async () => {
      if (!provider.client || !customer.data)
        throw new Error("Provider or customer not defined");

      const filename = `${customer.data.id}_gallery_hack`;
      const { error } = await uploadWithContentType(
        provider.client,
        "provider_gallery",
        filename,
        testImageBuffer
      );

      expect(error).not.toBeNull();
    });

    test("provider cannot update other user's account avatar", async () => {
      if (!provider.client || !admin.client || !customer.data) {
        throw new Error("Provider, admin, or customer not defined");
      }

      const filename = customer.data.id;

      // Admin uploads file for customer
      await uploadWithContentType(
        admin.client,
        "account_avatar",
        filename,
        testImageBuffer
      );

      // Provider tries to update it
      const fileType = await fileTypeFromBuffer(testImageBuffer);
      const { error } = await provider.client.storage
        .from("account_avatar")
        .update(filename, testImageBuffer, {
          contentType: fileType?.mime || "application/octet-stream"
        });

      expect(error).not.toBeNull();

      // Clean up
      await admin.client.storage.from("account_avatar").remove([filename]);
    });

    test("customer cannot access catalog_activity bucket", async () => {
      if (!customer.client || !catalogActivity.id) {
        throw new Error("Customer or catalog activity not defined");
      }

      const { error } = await uploadWithContentType(
        customer.client,
        "catalog_activity",
        catalogActivity.id,
        testImageBuffer
      );

      expect(error).not.toBeNull();
    });
  });

  describe("Comprehensive Update Operations", () => {
    test("provider can update their own provider_gallery files", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const filename = `${provider.data.id}_update_gallery`;

      // Upload initial file
      await uploadWithContentType(
        provider.client,
        "provider_gallery",
        filename,
        testImageBuffer
      );

      // Update the file
      const fileType = await fileTypeFromBuffer(testImageBuffer);
      const { error } = await provider.client.storage
        .from("provider_gallery")
        .update(filename, testImageBuffer, {
          contentType: fileType?.mime || "application/octet-stream",
          upsert: true
        });

      expect(error).toBeNull();

      // Clean up
      await provider.client.storage.from("provider_gallery").remove([filename]);
    });

    test("provider can update their own provider_activity files", async () => {
      if (!provider.client || !provider.data || !catalogActivity.id) {
        throw new Error("Provider or catalog activity not defined");
      }

      const filename = `${provider.data.id}_${catalogActivity.id}`;

      // Upload initial file
      await uploadWithContentType(
        provider.client,
        "provider_activity",
        filename,
        testImageBuffer
      );

      // Update the file
      const fileType = await fileTypeFromBuffer(testImageBuffer);
      const { error } = await provider.client.storage
        .from("provider_activity")
        .update(filename, testImageBuffer, {
          contentType: fileType?.mime || "application/octet-stream",
          upsert: true
        });

      expect(error).toBeNull();

      // Clean up
      await provider.client.storage
        .from("provider_activity")
        .remove([filename]);
    });

    test("provider can update their own provider_voice files", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const filename = provider.data.id;

      // Upload initial file
      await uploadWithContentType(
        provider.client,
        "provider_voice",
        filename,
        testVoiceBuffer
      );

      // Update the file
      const fileType = await fileTypeFromBuffer(testVoiceBuffer);
      const { error } = await provider.client.storage
        .from("provider_voice")
        .update(filename, testVoiceBuffer, {
          contentType: fileType?.mime || "application/octet-stream",
          upsert: true
        });

      expect(error).toBeNull();

      // Clean up
      await provider.client.storage.from("provider_voice").remove([filename]);
    });

    test("admin can update catalog_activity files", async () => {
      if (!admin.client || !catalogActivity.id) {
        throw new Error("Admin or catalog activity not defined");
      }

      const filename = catalogActivity.id;

      // Upload initial file
      await uploadWithContentType(
        admin.client,
        "catalog_activity",
        filename,
        testImageBuffer
      );

      // Update the file
      const fileType = await fileTypeFromBuffer(testImageBuffer);
      const { error } = await admin.client.storage
        .from("catalog_activity")
        .update(filename, testImageBuffer, {
          contentType: fileType?.mime || "application/octet-stream",
          upsert: true
        });

      expect(error).toBeNull();

      // Clean up
      await admin.client.storage.from("catalog_activity").remove([filename]);
    });
  });

  describe("Comprehensive Delete Operations", () => {
    test("provider can delete their own provider_gallery files", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const filename = `${provider.data.id}_delete_gallery`;

      // Upload file
      await uploadWithContentType(
        provider.client,
        "provider_gallery",
        filename,
        testImageBuffer
      );

      // Delete the file
      const { error } = await provider.client.storage
        .from("provider_gallery")
        .remove([filename]);

      expect(error).toBeNull();

      // Verify file was deleted
      const { data: objects } = await provider.client.storage
        .from("provider_gallery")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(false);
    });

    test("provider can delete their own provider_activity files", async () => {
      if (!provider.client || !provider.data || !catalogActivity.id) {
        throw new Error("Provider or catalog activity not defined");
      }

      const filename = `${provider.data.id}_${catalogActivity.id}`;

      // Upload file
      await uploadWithContentType(
        provider.client,
        "provider_activity",
        filename,
        testImageBuffer
      );

      // Delete the file
      const { error } = await provider.client.storage
        .from("provider_activity")
        .remove([filename]);

      expect(error).toBeNull();

      // Verify file was deleted
      const { data: objects } = await provider.client.storage
        .from("provider_activity")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(false);
    });

    test("provider can delete their own provider_voice files", async () => {
      if (!provider.client || !provider.data)
        throw new Error("Provider not defined");

      const filename = `${provider.data.id}_voice_delete`;

      // Upload file
      await uploadWithContentType(
        provider.client,
        "provider_voice",
        filename,
        testVoiceBuffer
      );

      // Delete the file
      const { error } = await provider.client.storage
        .from("provider_voice")
        .remove([filename]);

      expect(error).toBeNull();

      // Verify file was deleted
      const { data: objects } = await provider.client.storage
        .from("provider_voice")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(false);
    });

    test("admin can delete catalog_activity files", async () => {
      if (!admin.client || !catalogActivity.id) {
        throw new Error("Admin or catalog activity not defined");
      }

      const filename = catalogActivity.id;

      // Upload file
      await uploadWithContentType(
        admin.client,
        "catalog_activity",
        filename,
        testImageBuffer
      );

      // Delete the file
      const { error } = await admin.client.storage
        .from("catalog_activity")
        .remove([filename]);

      expect(error).toBeNull();

      // Verify file was deleted
      const { data: objects } = await admin.client.storage
        .from("catalog_activity")
        .list("", { search: filename });

      expect(objects?.some((obj) => obj.name === filename)).toBe(false);
    });
  });
});
