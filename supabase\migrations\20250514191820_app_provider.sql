-- section <PERSON>HEMA
DROP SCHEMA IF EXISTS app_provider CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_provider
;

GRANT USAGE ON SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section ENUMS
-- anchor APPLICATION_STATUS
CREATE TYPE app_provider.APPLICATION_STATUS AS ENUM(
  'draft',
  'submitted',
  'approved',
  'rejected'
)
;

-- anchor ORDER_STATUS
CREATE TYPE app_provider.ORDER_STATUS AS ENUM(
  'pending',
  'accepted',
  'rejected',
  'completed',
  'cancelled',
  'in_dispute',
  'refunded'
)
;

-- anchor DISPUTE_ACTION
CREATE TYPE app_provider.DISPUTE_ACTION AS ENUM('refund', 'release')
;

-- anchor AVAILABILITY_TYPE
CREATE TYPE app_provider.AVAILABILITY_TYPE AS ENUM(
  'individual_day',
  'weekdays',
  'weekends'
)
;

-- !section
-- section DOMAIN
-- anchor RATING
CREATE DOMAIN app_provider.RATING AS INTEGER CHECK (
  VALUE >= 0
  AND VALUE <= 5
)
;

-- !section
-- section COLUMN FUNCTIONS
-- anchor calculate_disputable_window
CREATE OR REPLACE FUNCTION app_provider.calculate_disputable_window (ts TIMESTAMPTZ) RETURNS TIMESTAMPTZ AS $$
DECLARE
  v_dispute_window_hours INTEGER;
BEGIN
  -- Fetch dispute_window_hours from app_transaction.config
  SELECT dispute_window_hours
  INTO v_dispute_window_hours
  FROM app_transaction.config
  LIMIT 1;

  -- Calculate the disputable until timestamp
  RETURN ts + (v_dispute_window_hours || ' hours')::INTERVAL;
END;
$$ LANGUAGE plpgsql IMMUTABLE
;

-- !section
-- section TABLES
-- anchor config
CREATE TABLE app_provider.config (
  id BOOLEAN PRIMARY KEY DEFAULT TRUE CHECK (id), -- Always one row
  cap_reward_to_customer_for_review_submission app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  cap_reward_to_provider_for_review_approval app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  cap_reward_to_customer_for_completed_order app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  cap_reward_to_provider_for_completed_order app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  cap_cost_for_question_submission app_transaction.TOKEN_UNIT NOT NULL DEFAULT 100,
  cap_reward_for_question_answer app_transaction.TOKEN_UNIT NOT NULL DEFAULT 100
)
;

-- anchor profile
CREATE TABLE app_provider.profile (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  slug app_core.SLUG UNIQUE NOT NULL,
  bio JSONB
)
;

-- anchor application
CREATE TABLE app_provider.application (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  application_status app_provider.APPLICATION_STATUS NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor approved_user
CREATE TABLE app_provider.approved_user (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id)
)
;

-- anchor activity
CREATE TABLE app_provider.activity (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES app_catalog.activity (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (user_id, activity_id)
)
;

COMMENT ON TABLE app_provider.activity IS 'Stores activities selected by providers and their custom descriptions.'
;

-- anchor service
CREATE TABLE app_provider.service (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  activity_id UUID REFERENCES app_provider.activity (id) ON DELETE CASCADE,
  selected_service_id UUID REFERENCES app_catalog.service (id) ON DELETE SET NULL,
  NAME JSONB,
  description JSONB,
  status app_core.POST_STATUS NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  soda_amount app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  pricing_id UUID REFERENCES app_catalog.pricing (id) ON DELETE SET NULL,
  max_unit_count INTEGER NOT NULL DEFAULT 1 CHECK (max_unit_count > 0),
  CONSTRAINT check_published_service_completeness CHECK (
    status != 'published'
    OR (
      (
        (
          name IS NOT NULL
          AND name != '{}'::JSONB
        )
        OR selected_service_id IS NOT NULL
      )
      AND (pricing_id IS NOT NULL)
    )
  )
)
;

-- anchor service_modifier
CREATE TABLE app_provider.service_modifier (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES app_provider.activity (id) ON DELETE CASCADE,
  NAME JSONB NOT NULL,
  description JSONB,
  status app_core.POST_STATUS NOT NULL DEFAULT 'draft',
  soda_amount app_transaction.TOKEN_UNIT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor order
CREATE TABLE app_provider.order (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  escrow_id UUID UNIQUE REFERENCES app_transaction.escrow (id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  receiver_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  service_id UUID NOT NULL REFERENCES app_provider.service (id) ON DELETE CASCADE,
  order_status app_provider.ORDER_STATUS NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  soda_amount app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  unit_count INTEGER NOT NULL DEFAULT 1 CHECK (unit_count > 0),
  order_details JSONB,
  disputable_until TIMESTAMPTZ GENERATED ALWAYS AS (
    app_provider.calculate_disputable_window (
      COALESCE(
        completed_at,
        TIMESTAMPTZ '2000-01-01 00:00:00+00'
      )
    )
  ) STORED,
  next_status_for_sender app_provider.ORDER_STATUS[] GENERATED ALWAYS AS (
    CASE
      WHEN order_status = 'pending' THEN ARRAY[
        'cancelled'::app_provider.ORDER_STATUS
      ]
      WHEN order_status = 'accepted' THEN ARRAY[
        'in_dispute'::app_provider.ORDER_STATUS
      ]
      WHEN order_status = 'completed' THEN ARRAY[
        'in_dispute'::app_provider.ORDER_STATUS
      ]
      ELSE ARRAY[]::app_provider.ORDER_STATUS[]
    END
  ) STORED,
  next_status_for_receiver app_provider.ORDER_STATUS[] GENERATED ALWAYS AS (
    CASE
      WHEN order_status = 'pending' THEN ARRAY[
        'accepted'::app_provider.ORDER_STATUS,
        'rejected'::app_provider.ORDER_STATUS
      ]
      WHEN order_status = 'accepted' THEN ARRAY[
        'completed'::app_provider.ORDER_STATUS
      ]
      WHEN order_status = 'completed' THEN ARRAY[
        'refunded'::app_provider.ORDER_STATUS
      ]
      WHEN order_status = 'in_dispute' THEN ARRAY[
        'refunded'::app_provider.ORDER_STATUS
      ]
      ELSE ARRAY[]::app_provider.ORDER_STATUS[]
    END
  ) STORED
)
;

-- anchor order_archive
-- Copy order table.
CREATE TABLE app_provider.order_archive AS TABLE app_provider.order
WITH
  NO DATA
;

ALTER TABLE app_provider.order_archive
ADD PRIMARY KEY (id)
;

-- anchor review_pass
CREATE TABLE app_provider.review_pass (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NULL REFERENCES auth.users (id) ON DELETE SET NULL,
  order_id UUID NOT NULL,
  provider_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES app_provider.activity (id) ON DELETE SET NULL,
  order_details JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor review
CREATE TABLE app_provider.review (
  id UUID PRIMARY KEY REFERENCES app_provider.review_pass (id) ON DELETE CASCADE,
  user_id UUID NULL REFERENCES auth.users (id) ON DELETE SET NULL,
  rating app_provider.RATING NOT NULL,
  comment TEXT,
  comment_locale app_core.LOCALE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor approved_service
CREATE TABLE app_provider.approved_service (
  service_id UUID PRIMARY KEY REFERENCES app_provider.service (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor approved_service_modifier
CREATE TABLE app_provider.approved_service_modifier (
  service_modifier_id UUID PRIMARY KEY REFERENCES app_provider.service_modifier (id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor approved_review
CREATE TABLE app_provider.approved_review (
  review_id UUID PRIMARY KEY REFERENCES app_provider.review (id) ON DELETE CASCADE,
  user_id UUID NULL REFERENCES auth.users (id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor performance
CREATE TABLE app_provider.performance (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  rating app_provider.RATING NOT NULL DEFAULT 0,
  reviews INTEGER NOT NULL DEFAULT 0,
  completed_orders INTEGER NOT NULL DEFAULT 0,
  refunds INTEGER NOT NULL DEFAULT 0,
  refunds_by_intervention INTEGER NOT NULL DEFAULT 0,
  response_time INTERVAL,
  earned_soda app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0,
  answered_questions INTEGER NOT NULL DEFAULT 0
)
;

-- anchor activity_performance
CREATE TABLE app_provider.activity_performance (
  activity_id UUID PRIMARY KEY REFERENCES app_provider.activity (id) ON DELETE CASCADE,
  rating app_provider.RATING NOT NULL DEFAULT 0,
  reviews INTEGER NOT NULL DEFAULT 0,
  completed_orders INTEGER NOT NULL DEFAULT 0
)
;

-- anchor provider_status
CREATE TABLE app_provider.status (
  user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
  is_open_for_orders BOOLEAN NOT NULL DEFAULT FALSE,
  notes JSONB,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor order_log
CREATE TABLE app_provider.order_log (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  order_id TEXT NOT NULL,
  old_status app_provider.ORDER_STATUS,
  new_status app_provider.ORDER_STATUS,
  changed_by UUID REFERENCES auth.users (id) ON DELETE SET NULL,
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor availability
CREATE TABLE app_provider.availability (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  start_time TIME WITH TIME ZONE NOT NULL,
  end_time TIME WITH TIME ZONE NOT NULL,
  availability_type app_provider.AVAILABILITY_TYPE NOT NULL,
  day_of_week app_core.DAY_OF_WEEK,
  CONSTRAINT start_time_before_end_time CHECK (start_time < end_time),
  CONSTRAINT min_duration_15_minutes CHECK (
    ('2000-01-01'::DATE + end_time) - (
      '2000-01-01'::DATE + start_time
    ) >= INTERVAL '15 minutes'
  ),
  CONSTRAINT day_of_week_assignable CHECK (
    (
      availability_type = 'individual_day'
      AND day_of_week IS NOT NULL
    )
    OR (
      availability_type != 'individual_day'
      AND day_of_week IS NULL
    )
  )
)
;

-- anchor question
CREATE TABLE app_provider.question (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  asker_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  provider_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  question_text TEXT NOT NULL CHECK (
    LENGTH(question_text) >= 10
    AND LENGTH(question_text) <= 1000
  ),
  cap_cost app_transaction.TOKEN_UNIT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT asker_provider_check CHECK (asker_id <> provider_id)
)
;

-- anchor question_answer
CREATE TABLE app_provider.question_answer (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  question_id UUID NOT NULL REFERENCES app_provider.question (id) ON DELETE CASCADE,
  provider_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  answer_text TEXT NOT NULL CHECK (
    LENGTH(answer_text) >= 10
    AND LENGTH(answer_text) <= 2000
  ),
  cap_reward app_transaction.TOKEN_UNIT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE (question_id) -- Each question can only have one answer
)
;

-- anchor field_value
CREATE TABLE app_provider.field_value (
  id UUID PRIMARY KEY DEFAULT GEN_RANDOM_UUID(),
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES app_provider.activity (id) ON DELETE CASCADE,
  field_id UUID NOT NULL REFERENCES app_catalog.field (id) ON DELETE CASCADE,
  field_option_id UUID REFERENCES app_catalog.field_option (id) ON DELETE SET NULL,
  TEXT TEXT,
  checkbox BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor user_favorite
CREATE TABLE app_provider.user_favorite (
  user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
  provider_id UUID NOT NULL REFERENCES app_provider.approved_user (user_id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, provider_id),
  CHECK (user_id <> provider_id)
)
;

COMMENT ON TABLE app_provider.user_favorite IS 'Stores user favorites for providers. Users can favorite providers to easily find them later.'
;

-- !section
-- section TRIGGER FUNCTIONS
-- anchor check_availability_overlap
CREATE OR REPLACE FUNCTION app_provider.check_availability_overlap () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM app_provider.availability existing
    WHERE
      existing.user_id = NEW.user_id
      AND existing.id != NEW.id
      AND (
        (NEW.availability_type = 'individual_day' AND
         existing.availability_type = 'individual_day' AND
         NEW.day_of_week = existing.day_of_week)

        OR (NEW.availability_type = 'individual_day' AND
            existing.availability_type = 'weekdays' AND
            NEW.day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday'))

        OR (NEW.availability_type = 'individual_day' AND
            existing.availability_type = 'weekends' AND
            NEW.day_of_week IN ('sunday', 'saturday'))

        OR (NEW.availability_type = 'weekdays' AND
            existing.availability_type = 'individual_day' AND
            existing.day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday'))

        OR (NEW.availability_type = 'weekdays' AND
            existing.availability_type = 'weekdays')

        OR (NEW.availability_type = 'weekends' AND
            existing.availability_type = 'individual_day' AND
            existing.day_of_week IN ('sunday', 'saturday'))

        OR (NEW.availability_type = 'weekends' AND
            existing.availability_type = 'weekends')
      )
      AND NEW.start_time < existing.end_time
      AND existing.start_time < NEW.end_time
  ) THEN
    RAISE EXCEPTION 'Availability entry overlaps with an existing entry.';
  END IF;

  RETURN NEW;
END;
$$
;

-- anchor log_order_status_change
CREATE OR REPLACE FUNCTION app_provider.log_order_status_change () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Only log if order_status has changed
  INSERT INTO app_provider.order_log (order_id, old_status, new_status, changed_by)
  VALUES (NEW.id::TEXT, OLD.order_status, NEW.order_status, auth.uid());
  RETURN NULL;
END;
$$
;

-- anchor log_order_insertion
CREATE OR REPLACE FUNCTION app_provider.log_order_insertion () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Log the initial order creation with NULL old_status
  INSERT INTO app_provider.order_log (order_id, old_status, new_status, changed_by)
  VALUES (NEW.id::TEXT, NULL, NEW.order_status, auth.uid());
  RETURN NULL;
END;
$$
;

-- anchor handle_application_approved
-- This function inserts a record into app_provider.approved when an application is approved.
CREATE OR REPLACE FUNCTION app_provider.handle_application_approved () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  IF NEW.application_status = 'approved' AND (OLD IS NULL OR OLD.application_status <> 'approved') THEN
    INSERT INTO app_provider.approved_user (user_id)
    VALUES (NEW.user_id)
    ON CONFLICT (user_id) DO NOTHING;

    -- Set status of all services associated with the user to 'published'
    UPDATE app_provider.service
    SET status = 'published'
    WHERE user_id = NEW.user_id AND status = 'draft';

    -- Set status of all service modifiers associated with the user to 'published'
    UPDATE app_provider.service_modifier
    SET status = 'published'
    WHERE user_id = NEW.user_id AND status = 'draft';

    -- Approve all services associated with the user
    INSERT INTO app_provider.approved_service (service_id, user_id)
    SELECT id, NEW.user_id FROM app_provider.service WHERE user_id = NEW.user_id
    ON CONFLICT (service_id) DO NOTHING;

    -- Approve all service modifiers associated with the user
    INSERT INTO app_provider.approved_service_modifier (service_modifier_id, user_id)
    SELECT id, NEW.user_id FROM app_provider.service_modifier WHERE user_id = NEW.user_id
    ON CONFLICT (service_modifier_id) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$
;

-- anchor process_order_completion_and_review_pass
CREATE OR REPLACE FUNCTION app_provider.process_order_completion_and_review_pass () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_activity_id UUID;
BEGIN
  IF NEW.order_status = 'completed' AND OLD.order_status <> 'completed' THEN
    NEW.completed_at = NOW();

    -- Get the activity_id from the service
    SELECT activity_id
    INTO v_activity_id
    FROM app_provider.service
    WHERE id = NEW.service_id;

    -- Insert a row into app_provider.review_pass
    INSERT INTO app_provider.review_pass (
      user_id,
      order_id,
      provider_id,
      activity_id,
      order_details
    ) VALUES (
      NEW.sender_id,
      NEW.id,
      NEW.receiver_id,
      v_activity_id,
      NEW.order_details
    );
  END IF;
  RETURN NEW;
END;
$$
;

-- anchor handle_order_refund_on_update_or_delete
CREATE OR REPLACE FUNCTION app_provider.handle_order_refund_on_update_or_delete () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Check if the order is being deleted OR if the status is changing to a refunded state
  IF (TG_OP = 'DELETE' AND OLD.order_status IN ('pending', 'accepted')) OR
     (TG_OP = 'UPDATE' AND NEW.order_status IN ('rejected', 'cancelled', 'refunded') AND OLD.order_status NOT IN ('rejected', 'cancelled', 'refunded'))
  THEN
    -- If the order has an associated escrow, set its status to 'refunded'
    IF OLD.escrow_id IS NOT NULL THEN
      UPDATE app_transaction.escrow
      SET status = 'refunded'
      WHERE id = OLD.escrow_id;
    END IF;
  END IF;

  -- For DELETE operations, return OLD
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  END IF;

  -- For UPDATE operations, return NEW
  RETURN NEW;
END;
$$
;

-- anchor handle_order_refund_update_performance
CREATE OR REPLACE FUNCTION app_provider.handle_order_refund_update_performance () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
BEGIN
  -- Only proceed if the order status is changing to 'refunded'
  IF NEW.order_status = 'refunded' AND OLD.order_status IS DISTINCT FROM 'refunded' THEN
    -- Ensure a performance record exists for the provider
    INSERT INTO app_provider.performance (user_id)
    VALUES (NEW.receiver_id)
    ON CONFLICT (user_id) DO NOTHING;

    -- Determine who initiated the refund
    IF v_current_user_id = NEW.receiver_id THEN
      -- Provider initiated the refund
      UPDATE app_provider.performance
      SET refunds = refunds + 1
      WHERE user_id = NEW.receiver_id;
    ELSE
      -- Refund initiated by someone else (e.g., admin)
      UPDATE app_provider.performance
      SET refunds_by_intervention = refunds_by_intervention + 1
      WHERE user_id = NEW.receiver_id;
    END IF;
  END IF;
  RETURN NEW;
END;
$$
;

-- anchor handle_new_order_with_escrow
CREATE OR REPLACE FUNCTION app_provider.handle_new_order_with_escrow () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  v_escrow_id UUID;
BEGIN
  -- Insert a new escrow record
  INSERT INTO app_transaction.escrow (sender_id, receiver_id, soda_amount)
  VALUES (NEW.sender_id, NEW.receiver_id, NEW.soda_amount)
  RETURNING id INTO v_escrow_id;

  -- Bind the new escrow id to the order row
  NEW.escrow_id = v_escrow_id;

  RETURN NEW;
END;
$$
;

-- anchor archive_order_and_update_performance
-- This function archives deleted order rows into app_provider.order_archive and updates provider and activity performance metrics.
CREATE OR REPLACE FUNCTION app_provider.archive_order_and_update_performance () RETURNS TRIGGER AS $$
DECLARE
  v_cap_reward_to_customer app_transaction.TOKEN_UNIT;
  v_cap_reward_to_provider app_transaction.TOKEN_UNIT;
  v_activity_id UUID;
BEGIN
  INSERT INTO app_provider.order_archive
  SELECT OLD.*;

  -- Update performance metrics if the order was completed
  IF OLD.order_status = 'completed' THEN
    -- Get the activity_id from the service
    SELECT activity_id
    INTO v_activity_id
    FROM app_provider.service
    WHERE id = OLD.service_id;

    -- Get cap reward amounts from config
    SELECT
      cap_reward_to_customer_for_completed_order,
      cap_reward_to_provider_for_completed_order
    INTO
      v_cap_reward_to_customer,
      v_cap_reward_to_provider
    FROM app_provider.config
    LIMIT 1;

    -- Reward customer
    IF v_cap_reward_to_customer > 0 THEN
        PERFORM app_transaction.ensure_wallet_exists(OLD.sender_id);
        UPDATE app_transaction.wallet
        SET cap_balance = cap_balance + v_cap_reward_to_customer
        WHERE user_id = OLD.sender_id;
    END IF;

    -- Ensure a performance record exists for the provider
    INSERT INTO app_provider.performance (user_id)
    VALUES (OLD.receiver_id)
    ON CONFLICT (user_id) DO NOTHING;

    -- Update provider's performance metrics
    UPDATE app_provider.performance
    SET
      completed_orders = completed_orders + 1,
      earned_soda = earned_soda + OLD.soda_amount
    WHERE user_id = OLD.receiver_id;

    -- Ensure an activity performance record exists
    INSERT INTO app_provider.activity_performance (activity_id)
    VALUES (v_activity_id)
    ON CONFLICT (activity_id) DO NOTHING;

    -- Update activity performance metrics
    UPDATE app_provider.activity_performance
    SET completed_orders = completed_orders + 1
    WHERE activity_id = v_activity_id;

    -- Reward provider
    IF v_cap_reward_to_provider > 0 THEN
        PERFORM app_transaction.ensure_wallet_exists(OLD.receiver_id);
        UPDATE app_transaction.wallet
        SET cap_balance = cap_balance + v_cap_reward_to_provider
        WHERE user_id = OLD.receiver_id;
    END IF;
  END IF;

  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
;

-- anchor assign_provider_applicant_role_trigger_function
CREATE OR REPLACE FUNCTION app_provider.assign_provider_applicant_role_trigger_function () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_access.assign_role_to_user(NEW.user_id, 'provider_applicant');
  RETURN NEW;
END;
$$
;

-- anchor assign_provider_applicant_under_review_role_trigger_function
CREATE OR REPLACE FUNCTION app_provider.assign_provider_applicant_under_review_role_trigger_function () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  IF NEW.application_status = 'submitted' AND OLD.application_status = 'draft' THEN
    PERFORM app_access.assign_role_to_user(NEW.user_id, 'provider_applicant_under_review');
    PERFORM app_access.revoke_role_from_user(NEW.user_id, 'provider_applicant');
  END IF;
  RETURN NEW;
END;
$$
;

-- anchor assign_provider_role_on_approved_user_insert
CREATE OR REPLACE FUNCTION app_provider.assign_provider_role_on_approved_user_insert () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_access.assign_role_to_user(NEW.user_id, 'provider');
  RETURN NEW;
END;
$$
;

-- anchor revoke_provider_role_on_approved_user_delete
CREATE OR REPLACE FUNCTION app_provider.revoke_provider_role_on_approved_user_delete () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_access.revoke_role_from_user(OLD.user_id, 'provider');
  RETURN OLD;
END;
$$
;

-- anchor check_required_fields_on_application_submit
-- This function ensures that a provider has a profile, at least one service, avatar, voice, and at least 3 gallery images before submitting their application.
CREATE OR REPLACE FUNCTION app_provider.check_required_fields_on_application_submit () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_has_profile BOOLEAN;
  v_has_service BOOLEAN;
  v_has_avatar BOOLEAN;
  v_has_voice BOOLEAN;
  v_gallery_count INTEGER;
BEGIN
  IF NEW.application_status = 'submitted' AND OLD.application_status = 'draft' THEN
    -- Check if the user has a profile
    SELECT EXISTS (SELECT 1 FROM app_provider.profile WHERE user_id = NEW.user_id) INTO v_has_profile;

    -- Check if the user has at least one service
    SELECT EXISTS (SELECT 1 FROM app_provider.service WHERE user_id = NEW.user_id) INTO v_has_service;

    -- Check if the user has an avatar
    SELECT EXISTS (
      SELECT 1 FROM storage.objects
      WHERE bucket_id = 'account_avatar'
      AND (storage.filename(name)) = NEW.user_id::TEXT
    ) INTO v_has_avatar;

    -- Check if the user has a voice file
    SELECT EXISTS (
      SELECT 1 FROM storage.objects
      WHERE bucket_id = 'provider_voice'
      AND (storage.filename(name)) = NEW.user_id::TEXT
    ) INTO v_has_voice;

    -- Check if the user has at least 3 gallery images
    SELECT COUNT(*) FROM storage.objects
    WHERE bucket_id = 'provider_gallery'
    AND (storage.filename(name)) LIKE NEW.user_id::TEXT || '_%'
    INTO v_gallery_count;

    IF NOT v_has_profile THEN
      RAISE EXCEPTION 'Cannot submit application: Provider must have a profile.';
    END IF;

    IF NOT v_has_service THEN
      RAISE EXCEPTION 'Cannot submit application: Provider must have at least one service.';
    END IF;

    IF NOT v_has_avatar THEN
      RAISE EXCEPTION 'Cannot submit application: Provider must have an avatar.';
    END IF;

    IF NOT v_has_voice THEN
      RAISE EXCEPTION 'Cannot submit application: Provider must have a voice file.';
    END IF;

    IF v_gallery_count < 3 THEN
      RAISE EXCEPTION 'Cannot submit application: Provider must have at least 3 gallery images. Currently has %.', v_gallery_count;
    END IF;
  END IF;
  RETURN NEW;
END;
$$
;

-- anchor revoke_applicant_roles_on_rejection
CREATE OR REPLACE FUNCTION app_provider.revoke_applicant_roles_on_rejection () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  IF NEW.application_status = 'rejected' AND OLD.application_status <> 'rejected' THEN
    -- Revoke the 'provider_applicant_under_review' role
    PERFORM app_access.revoke_role_from_user(NEW.user_id, 'provider_applicant_under_review');
    -- Also revoke 'provider_applicant' in case it was not revoked previously
    PERFORM app_access.revoke_role_from_user(NEW.user_id, 'provider_applicant');

    -- Delete related provider data
    DELETE FROM app_provider.profile WHERE user_id = NEW.user_id;
    DELETE FROM app_provider.activity WHERE user_id = NEW.user_id;
    DELETE FROM app_provider.service WHERE user_id = NEW.user_id;
    DELETE FROM app_provider.service_modifier WHERE user_id = NEW.user_id;
    DELETE FROM app_provider.field_value WHERE user_id = NEW.user_id;

  END IF;
  RETURN NEW;
END;
$$
;

-- anchor handle_new_review_reward_caps
CREATE OR REPLACE FUNCTION app_provider.handle_new_review_reward_caps () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_cap_reward_to_customer_for_review_submission app_transaction.TOKEN_UNIT;
BEGIN
  -- Get the cap reward amount from app_provider.config
  SELECT cap_reward_to_customer_for_review_submission
  INTO v_cap_reward_to_customer_for_review_submission
  FROM app_provider.config
  LIMIT 1;

  -- Ensure a wallet exists for the user
  PERFORM app_transaction.ensure_wallet_exists (NEW.user_id);

  -- Update the user's cap balance
  UPDATE app_transaction.wallet
  SET
    cap_balance = cap_balance + v_cap_reward_to_customer_for_review_submission
  WHERE
    user_id = NEW.user_id;

  RETURN NEW;
END;
$$
;

-- anchor handle_approved_review_update_performance
CREATE OR REPLACE FUNCTION app_provider.handle_approved_review_update_performance () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_provider_id UUID;
  v_activity_id UUID;
  v_new_rating app_provider.RATING;
  v_total_reviews INTEGER;
  v_activity_new_rating app_provider.RATING;
  v_activity_total_reviews INTEGER;
  v_cap_reward_to_provider_for_review_approval app_transaction.TOKEN_UNIT;
BEGIN
  -- Get provider_id and activity_id from review_pass using the review_id
  SELECT rp.provider_id, rp.activity_id
  INTO v_provider_id, v_activity_id
  FROM app_provider.review_pass rp
  WHERE rp.id = NEW.review_id;

  -- Calculate the new average rating and total reviews for the provider
  SELECT
    COALESCE(AVG(r.rating), 0)::app_provider.RATING,
    COUNT(ar.review_id)
  INTO
    v_new_rating,
    v_total_reviews
  FROM app_provider.approved_review ar
  JOIN app_provider.review r ON ar.review_id = r.id
  JOIN app_provider.review_pass rp ON r.id = rp.id
  WHERE rp.provider_id = v_provider_id;

  -- Calculate the new average rating and total reviews for the activity
  SELECT
    COALESCE(AVG(r.rating), 0)::app_provider.RATING,
    COUNT(ar.review_id)
  INTO
    v_activity_new_rating,
    v_activity_total_reviews
  FROM app_provider.approved_review ar
  JOIN app_provider.review r ON ar.review_id = r.id
  JOIN app_provider.review_pass rp ON r.id = rp.id
  WHERE rp.activity_id = v_activity_id;

  -- Ensure a performance record exists for the provider
  INSERT INTO app_provider.performance (user_id)
  VALUES (v_provider_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Update the provider's performance metrics
  UPDATE app_provider.performance
  SET
    rating = v_new_rating,
    reviews = v_total_reviews
  WHERE user_id = v_provider_id;

  -- Ensure an activity performance record exists
  INSERT INTO app_provider.activity_performance (activity_id)
  VALUES (v_activity_id)
  ON CONFLICT (activity_id) DO NOTHING;

  -- Update the activity's performance metrics
  UPDATE app_provider.activity_performance
  SET
    rating = v_activity_new_rating,
    reviews = v_activity_total_reviews
  WHERE activity_id = v_activity_id;

  -- Get the cap reward amount from app_provider.config
  SELECT cap_reward_to_provider_for_review_approval
  INTO v_cap_reward_to_provider_for_review_approval
  FROM app_provider.config
  LIMIT 1;

  -- Ensure a wallet exists for the provider
  PERFORM app_transaction.ensure_wallet_exists (v_provider_id);

  -- Update the provider's cap balance
  UPDATE app_transaction.wallet
  SET
    cap_balance = cap_balance + v_cap_reward_to_provider_for_review_approval
  WHERE
    user_id = v_provider_id;

  RETURN NEW;
END;
$$
;

-- anchor update_activity_service_count_on_approval
CREATE OR REPLACE FUNCTION app_provider.update_activity_service_count_on_approval () RETURNS TRIGGER AS $$
DECLARE
  v_catalog_activity_id UUID;
BEGIN
  -- On INSERT (service approved)
  IF TG_OP = 'INSERT' THEN
    SELECT pa.activity_id INTO v_catalog_activity_id
    FROM app_provider.service ps
    JOIN app_provider.activity pa ON ps.activity_id = pa.id
    WHERE ps.id = NEW.service_id;

    IF v_catalog_activity_id IS NOT NULL THEN
      UPDATE app_catalog.activity
      SET service_count = service_count + 1
      WHERE id = v_catalog_activity_id;
    END IF;
  -- On DELETE (service approval revoked)
  ELSIF TG_OP = 'DELETE' THEN
    SELECT pa.activity_id INTO v_catalog_activity_id
    FROM app_provider.service ps
    JOIN app_provider.activity pa ON ps.activity_id = pa.id
    WHERE ps.id = OLD.service_id;

    IF v_catalog_activity_id IS NOT NULL THEN
      UPDATE app_catalog.activity
      SET service_count = service_count - 1
      WHERE id = v_catalog_activity_id;
    END IF;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
;

-- anchor check_field_value_constraints
CREATE OR REPLACE FUNCTION app_provider.check_field_value_constraints () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
    v_field_type app_catalog.FIELD_TYPE;
    v_existing_count INTEGER;
BEGIN
    -- Get the field type from the app_catalog.field table
    SELECT type INTO v_field_type
    FROM app_catalog.field
    WHERE id = NEW.field_id;

    IF v_field_type IS NULL THEN
        RAISE EXCEPTION 'Field ID % not found in app_catalog.field.', NEW.field_id;
    END IF;

    CASE v_field_type
        WHEN 'text' THEN
            -- For 'text' type, only 'text' column should be filled
            IF NEW.text IS NULL OR NEW.checkbox IS NOT NULL OR NEW.field_option_id IS NOT NULL THEN
                RAISE EXCEPTION 'For field type "text", only the "text" column can be filled.';
            END IF;
            -- Ensure uniqueness for text fields per activity
            SELECT COUNT(*) INTO v_existing_count
            FROM app_provider.field_value
            WHERE user_id = NEW.user_id
              AND activity_id = NEW.activity_id
              AND field_id = NEW.field_id
              AND id IS DISTINCT FROM NEW.id; -- Exclude current row on UPDATE
            IF v_existing_count > 0 THEN
                RAISE EXCEPTION 'Only one text field value is allowed per activity for this field.';
            END IF;

        WHEN 'checkbox' THEN
            -- For 'checkbox' type, only 'checkbox' column should be filled
            IF NEW.checkbox IS NULL OR NEW.text IS NOT NULL OR NEW.field_option_id IS NOT NULL THEN
                RAISE EXCEPTION 'For field type "checkbox", only the "checkbox" column can be filled.';
            END IF;
            -- Ensure uniqueness for checkbox fields per activity
            SELECT COUNT(*) INTO v_existing_count
            FROM app_provider.field_value
            WHERE user_id = NEW.user_id
              AND activity_id = NEW.activity_id
              AND field_id = NEW.field_id
              AND id IS DISTINCT FROM NEW.id; -- Exclude current row on UPDATE
            IF v_existing_count > 0 THEN
                RAISE EXCEPTION 'Only one checkbox field value is allowed per activity for this field.';
            END IF;

        WHEN 'select' THEN
            -- For 'select' type, only 'field_option_id' should be filled
            IF NEW.field_option_id IS NULL OR NEW.text IS NOT NULL OR NEW.checkbox IS NOT NULL THEN
                RAISE EXCEPTION 'For field type "select", only the "field_option_id" column can be filled.';
            END IF;
            -- Ensure only one instance of field_value with the same activity_id for this field_id
            SELECT COUNT(*) INTO v_existing_count
            FROM app_provider.field_value
            WHERE user_id = NEW.user_id
              AND activity_id = NEW.activity_id
              AND field_id = NEW.field_id
              AND field_option_id IS NOT NULL -- Only count rows with an option selected
              AND id IS DISTINCT FROM NEW.id; -- Exclude current row on UPDATE
            IF v_existing_count > 0 THEN
                RAISE EXCEPTION 'Only one field option can be selected for a "select" type field per activity.';
            END IF;

        WHEN 'multiselect' THEN
            -- For 'multiselect' type, only 'field_option_id' should be filled
            IF NEW.field_option_id IS NULL OR NEW.text IS NOT NULL OR NEW.checkbox IS NOT NULL THEN
                RAISE EXCEPTION 'For field type "multiselect", only the "field_option_id" column can be filled.';
            END IF;
            -- Ensure uniqueness of field_option_id for multiselect within the same user, activity, and field
            SELECT COUNT(*) INTO v_existing_count
            FROM app_provider.field_value
            WHERE user_id = NEW.user_id
              AND activity_id = NEW.activity_id
              AND field_id = NEW.field_id
              AND field_option_id = NEW.field_option_id
              AND id IS DISTINCT FROM NEW.id; -- Exclude current row on UPDATE
            IF v_existing_count > 0 THEN
                RAISE EXCEPTION 'Duplicate field option selected for a "multiselect" type field within the same activity.';
            END IF;

        ELSE
            RAISE EXCEPTION 'Unsupported field type: %', v_field_type;
    END CASE;

    RETURN NEW;
END;
$$
;

-- anchor handle_new_question
CREATE OR REPLACE FUNCTION app_provider.handle_new_question () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_cap_cost app_transaction.TOKEN_UNIT;
  v_current_cap_balance app_transaction.TOKEN_UNIT;
BEGIN
  -- Get cap cost from config
  SELECT cap_cost_for_question_submission INTO v_cap_cost
  FROM app_provider.config
  LIMIT 1;

  -- Set the cap cost in the new record
  NEW.cap_cost = v_cap_cost;

  -- Ensure wallet exists for the asker
  PERFORM app_transaction.ensure_wallet_exists(NEW.asker_id);

  -- Check if asker has sufficient cap balance
  SELECT cap_balance INTO v_current_cap_balance
  FROM app_transaction.wallet
  WHERE user_id = NEW.asker_id;

  IF v_current_cap_balance < v_cap_cost THEN
    RAISE EXCEPTION 'Insufficient cap balance. Current: %, required: %', v_current_cap_balance, v_cap_cost;
  END IF;

  -- Deduct caps from asker's wallet
  UPDATE app_transaction.wallet
  SET cap_balance = cap_balance - v_cap_cost
  WHERE user_id = NEW.asker_id;

  RETURN NEW;
END;
$$
;

-- anchor handle_new_question_answer
CREATE OR REPLACE FUNCTION app_provider.handle_new_question_answer () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_cap_reward app_transaction.TOKEN_UNIT;
  v_question_provider_id UUID;
BEGIN
  -- Get cap reward from config
  SELECT cap_reward_for_question_answer INTO v_cap_reward
  FROM app_provider.config
  LIMIT 1;

  -- Set the cap reward in the new record
  NEW.cap_reward = v_cap_reward;

  -- Verify that the provider answering is the same as the question's target provider
  SELECT provider_id INTO v_question_provider_id
  FROM app_provider.question
  WHERE id = NEW.question_id;

  IF NEW.provider_id != v_question_provider_id THEN
    RAISE EXCEPTION 'You can only answer questions directed to you.';
  END IF;

  -- Ensure wallet exists for the provider
  PERFORM app_transaction.ensure_wallet_exists(NEW.provider_id);

  -- Reward caps to provider
  UPDATE app_transaction.wallet
  SET cap_balance = cap_balance + v_cap_reward
  WHERE user_id = NEW.provider_id;

  -- Update provider performance metrics
  INSERT INTO app_provider.performance (user_id)
  VALUES (NEW.provider_id)
  ON CONFLICT (user_id) DO NOTHING;

  UPDATE app_provider.performance
  SET answered_questions = answered_questions + 1
  WHERE user_id = NEW.provider_id;

  RETURN NEW;
END;
$$
;

-- !section
-- section FUNCTIONS
-- anchor update_order_status
CREATE OR REPLACE FUNCTION app_provider.update_order_status (
  p_order_id UUID,
  p_new_status app_provider.ORDER_STATUS,
  p_dispute_description TEXT DEFAULT NULL
) RETURNS app_provider.order LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_order app_provider.order;
  v_updated_order app_provider.order;
  v_ticket_id UUID;
BEGIN
  -- Get the current order
  SELECT * INTO v_order
  FROM app_provider.order
  WHERE id = p_order_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Order not found.';
  END IF;

  -- Allow updates if the status is not changing
  IF p_new_status = v_order.order_status THEN
    RETURN v_order;
  END IF;

  -- Check if dispute description is provided when changing to in_dispute
  IF p_new_status = 'in_dispute' AND p_dispute_description IS NULL THEN
    RAISE EXCEPTION 'Dispute description is required when changing order status to in_dispute.';
  END IF;

  -- Check if the current user is the sender
  IF v_current_user_id = v_order.sender_id THEN
    -- Check if user has permission to update orders as sender
    IF NOT app_access.has_capability('provider.order.submit') THEN
      RAISE EXCEPTION 'Insufficient permissions to update order status.';
    END IF;

    -- Check if the new status is allowed for the sender
    IF NOT (p_new_status = ANY(v_order.next_status_for_sender)) THEN
      RAISE EXCEPTION 'Invalid order status transition for sender from % to %.', v_order.order_status, p_new_status;
    END IF;

    -- Additional check for 'completed' to 'in_dispute' transition for sender
    IF v_order.order_status = 'completed' AND p_new_status = 'in_dispute' THEN
      IF NOW() > v_order.disputable_until THEN
        RAISE EXCEPTION 'Cannot dispute order after the disputable period has ended.';
      END IF;
    END IF;

  -- Check if the current user is the receiver
  ELSIF v_current_user_id = v_order.receiver_id THEN
    -- Check if user has permission to update orders as receiver
    IF NOT app_access.has_capability('provider.order.view') THEN
      RAISE EXCEPTION 'Insufficient permissions to update order status.';
    END IF;

    -- Check if the new status is allowed for the receiver
    IF NOT (p_new_status = ANY(v_order.next_status_for_receiver)) THEN
      RAISE EXCEPTION 'Invalid order status transition for receiver from % to %.', v_order.order_status, p_new_status;
    END IF;

  -- Check if the user has admin permissions
  ELSIF app_access.has_capability('provider.order.all.control') THEN
    -- Admin can make any status transition, no restrictions
    NULL; -- Do nothing, allow the update

  ELSE
    RAISE EXCEPTION 'You do not have permission to update this order.';
  END IF;

  -- Update the order status
  UPDATE app_provider.order
  SET order_status = p_new_status
  WHERE id = p_order_id
  RETURNING * INTO v_updated_order;

  -- Create a dispute ticket if the new status is 'in_dispute'
  IF p_new_status = 'in_dispute' AND p_dispute_description IS NOT NULL THEN
    INSERT INTO app_support.ticket (
      user_id,
      disputed_order_id,
      title,
      problem_description,
      type
    ) VALUES (
      v_current_user_id,
      p_order_id,
      'Order Dispute - Order #' || SUBSTRING(p_order_id::TEXT, 1, 8),
      p_dispute_description,
      'dispute'
    ) RETURNING id INTO v_ticket_id;
  END IF;

  RETURN v_updated_order;
END;
$$
;

-- anchor insert_order
CREATE OR REPLACE FUNCTION app_provider.insert_order (
  p_sender_id UUID,
  p_receiver_id UUID,
  p_service_id UUID,
  p_soda_amount app_transaction.TOKEN_UNIT,
  p_unit_count INTEGER,
  p_order_details JSONB
) RETURNS app_provider.order LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_new_order app_provider.order;
BEGIN
  -- Ensure it’s only called from submit_order
  IF current_setting('app.submit_order_active', true) IS DISTINCT FROM 'true' THEN
    RAISE EXCEPTION 'insert_order() can only be called from submit_order()';
  END IF;

  INSERT INTO app_provider.order (
    sender_id,
    receiver_id,
    service_id,
    soda_amount,
    unit_count,
    order_details
  ) VALUES (
    p_sender_id,
    p_receiver_id,
    p_service_id,
    p_soda_amount,
    p_unit_count,
    p_order_details
  )
  RETURNING * INTO v_new_order;

  RETURN v_new_order;
END;
$$
;

-- anchor submit_order
CREATE OR REPLACE FUNCTION app_provider.submit_order (
  p_service_id UUID,
  p_unit_count INTEGER,
  p_service_modifier_ids UUID[] DEFAULT NULL
) RETURNS app_provider.order AS $$
DECLARE
  v_sender_id UUID := auth.uid();
  v_receiver_id UUID;
  v_base_soda_amount app_transaction.TOKEN_UNIT;
  v_modifiers_soda_amount app_transaction.TOKEN_UNIT := 0;
  v_total_soda_amount app_transaction.TOKEN_UNIT;
  v_service_name JSONB;
  v_modifier_details JSONB;
  v_order_details JSONB;
  v_new_order app_provider.order;
  v_service_pricing_name JSONB;
  v_is_open_for_orders BOOLEAN;
BEGIN
  SELECT
    s.user_id,
    s.soda_amount,
    s.name,
    p.name AS pricing_name
  INTO
    v_receiver_id,
    v_base_soda_amount,
    v_service_name,
    v_service_pricing_name
  FROM app_provider.service s
  JOIN app_catalog.pricing p ON s.pricing_id = p.id
  WHERE s.id = p_service_id;

  -- Check if service exists
  IF v_receiver_id IS NULL THEN
    RAISE EXCEPTION 'Service with ID % not found.', p_service_id;
  END IF;

  -- Check if the provider is open for orders
  SELECT is_open_for_orders
  INTO v_is_open_for_orders
  FROM app_provider.status
  WHERE user_id = v_receiver_id;

  IF NOT FOUND OR NOT v_is_open_for_orders THEN
    RAISE EXCEPTION 'Provider is not currently open for orders.';
  END IF;

  -- If service modifiers are provided, sum their soda_amounts
  IF p_service_modifier_ids IS NOT NULL AND array_length(p_service_modifier_ids, 1) > 0 THEN
    -- Sum modifier soda_amounts for total calculation
    SELECT COALESCE(SUM(soda_amount), 0)
    INTO v_modifiers_soda_amount
    FROM app_provider.service_modifier
    WHERE id = ANY(p_service_modifier_ids);

    -- Fetch modifier details for order_details JSONB
    SELECT jsonb_agg(jsonb_build_object(
      'id', id,
      'name', name,
      'soda_amount', soda_amount
    ))
    INTO v_modifier_details
    FROM app_provider.service_modifier
    WHERE id = ANY(p_service_modifier_ids);

    -- Check to ensure all provided modifier IDs exist
    IF (SELECT COUNT(*) FROM app_provider.service_modifier WHERE id = ANY(p_service_modifier_ids)) != array_length(p_service_modifier_ids, 1) THEN
        RAISE EXCEPTION 'One or more service modifier IDs not found.';
    END IF;
  END IF;

  -- Calculate total soda amount
  v_total_soda_amount := (v_base_soda_amount + v_modifiers_soda_amount) * p_unit_count;

  -- Construct order_details JSONB
  v_order_details := jsonb_build_object(
    'service', jsonb_build_object(
      'id', p_service_id,
      'name', v_service_name,
      'soda_amount', v_base_soda_amount,
      'pricing', v_service_pricing_name
    ),
    'modifiers', COALESCE(v_modifier_details, '[]'::JSONB), -- Use COALESCE to handle cases with no modifiers
    'unit_count', p_unit_count
  );

  -- Set a session variable to allow insert_order to be called
  PERFORM set_config('app.submit_order_active', 'true', true);

  -- Insert the new order using the helper function
  v_new_order := app_provider.insert_order(
    v_sender_id,
    v_receiver_id,
    p_service_id,
    v_total_soda_amount,
    p_unit_count,
    v_order_details
  );

  RETURN v_new_order;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER
;

-- anchor release_completed_order_escrow
-- This function releases escrow for completed orders whose disputable_until time has passed.
CREATE OR REPLACE FUNCTION app_provider.release_completed_order_escrow () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  -- Update escrow status to 'released' for completed orders past their dispute window
  UPDATE app_transaction.escrow e
  SET status = 'released'
  FROM app_provider.order o
  WHERE
    o.escrow_id = e.id
    AND o.order_status = 'completed'
    AND o.disputable_until <= NOW()
    AND e.status = 'pending';
END;
$$
;

-- anchor handle_disputed_order
-- This function handles disputed orders by either refunding the escrow or releasing it to the provider.
CREATE OR REPLACE FUNCTION app_provider.handle_disputed_order (
  p_order_id UUID,
  p_action app_provider.DISPUTE_ACTION
) RETURNS app_provider.order LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_order_record app_provider.order;
  v_current_user_id UUID := auth.uid();
BEGIN
  -- Check if the invoker has the capability
  IF NOT app_access.has_capability('provider.order.all.control') THEN
    RAISE EXCEPTION 'User does not have the necessary capability to control orders.';
  END IF;

  -- Fetch the order record
  SELECT *
  INTO v_order_record
  FROM app_provider.order
  WHERE id = p_order_id;

  -- Check if the order exists
  IF v_order_record IS NULL THEN
    RAISE EXCEPTION 'Order with ID % not found.', p_order_id;
  END IF;

  -- Check if the order is in dispute
  IF v_order_record.order_status <> 'in_dispute' THEN
    RAISE EXCEPTION 'Order with ID % is not in dispute.', p_order_id;
  END IF;

  -- Handle the action
  IF p_action = 'refund' THEN
    -- Update order status to 'refunded'
    UPDATE app_provider.order
    SET order_status = 'refunded'
    WHERE id = p_order_id
    RETURNING * INTO v_order_record;

  ELSIF p_action = 'release' THEN
    -- Update order status to 'completed'
    UPDATE app_provider.order
    SET order_status = 'completed'
    WHERE id = p_order_id
    RETURNING * INTO v_order_record;

    -- Update escrow status to 'released'
    UPDATE app_transaction.escrow
    SET status = 'released'
    WHERE id = v_order_record.escrow_id;

  END IF;

  RETURN v_order_record;
END;
$$
;

-- anchor is_pending_or_approved_provider
CREATE OR REPLACE FUNCTION app_provider.is_pending_or_approved (p_user_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql IMMUTABLE AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_provider.application
    WHERE user_id = p_user_id AND application_status = 'draft'
  ) OR EXISTS (
    SELECT 1
    FROM app_provider.approved_user
    WHERE user_id = p_user_id
  );
END;
$$
;

-- anchor is_user_approved_provider
CREATE OR REPLACE FUNCTION app_provider.is_user_approved (p_user_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql STABLE SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_provider.approved_user
    WHERE user_id = p_user_id
  );
END;
$$
;

-- anchor is_service_approved
CREATE OR REPLACE FUNCTION app_provider.is_service_approved (p_service_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql STABLE SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_provider.approved_service
    WHERE service_id = p_service_id
  );
END;
$$
;

-- anchor is_service_modifier_approved
CREATE OR REPLACE FUNCTION app_provider.is_service_modifier_approved (p_service_modifier_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql STABLE SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_provider.approved_service_modifier
    WHERE service_modifier_id = p_service_modifier_id
  );
END;
$$
;

-- anchor is_review_approved
CREATE OR REPLACE FUNCTION app_provider.is_review_approved (p_review_id UUID) RETURNS BOOLEAN LANGUAGE plpgsql STABLE SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_provider.approved_review
    WHERE review_id = p_review_id
  );
END;
$$
;

REVOKE
EXECUTE ON FUNCTION app_provider.release_completed_order_escrow ()
FROM
  PUBLIC,
  anon,
  authenticated
;

-- anchor submit_question
CREATE OR REPLACE FUNCTION app_provider.submit_question (
  p_provider_id UUID,
  p_question_text TEXT
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_question_id UUID;
BEGIN
  -- Check if user has the capability to ask questions
  IF NOT app_access.has_capability('provider.question.submit') THEN
    RAISE EXCEPTION 'You do not have permission to ask questions to providers.';
  END IF;

  -- Validate that the provider exists and is approved
  IF NOT EXISTS (
    SELECT 1 FROM app_provider.approved_user
    WHERE user_id = p_provider_id
  ) THEN
    RAISE EXCEPTION 'Provider not found or not approved.';
  END IF;

  -- Validate that user is not asking themselves
  IF v_current_user_id = p_provider_id THEN
    RAISE EXCEPTION 'You cannot ask questions to yourself.';
  END IF;

  -- Insert the question (trigger will handle cap deduction)
  INSERT INTO app_provider.question (
    asker_id,
    provider_id,
    question_text
  ) VALUES (
    v_current_user_id,
    p_provider_id,
    p_question_text
  ) RETURNING id INTO v_question_id;

  RETURN v_question_id;
END;
$$
;

-- anchor submit_question_answer
CREATE OR REPLACE FUNCTION app_provider.submit_question_answer (
  p_question_id UUID,
  p_answer_text TEXT
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_question_provider_id UUID;
  v_answer_id UUID;
BEGIN
  -- Check if user has the capability to answer questions
  IF NOT app_access.has_capability('provider.question.answer') THEN
    RAISE EXCEPTION 'You do not have permission to answer questions.';
  END IF;

  -- Get question details and validate that current user is the provider
  SELECT provider_id INTO v_question_provider_id
  FROM app_provider.question
  WHERE id = p_question_id;

  IF v_question_provider_id IS NULL THEN
    RAISE EXCEPTION 'Question not found.';
  END IF;

  IF v_current_user_id != v_question_provider_id THEN
    RAISE EXCEPTION 'You can only answer questions directed to you.';
  END IF;

  -- Check if question has already been answered
  IF EXISTS (
    SELECT 1 FROM app_provider.question_answer
    WHERE question_id = p_question_id
  ) THEN
    RAISE EXCEPTION 'This question has already been answered.';
  END IF;

  -- Insert the answer (trigger will handle cap reward and performance update)
  INSERT INTO app_provider.question_answer (
    question_id,
    provider_id,
    answer_text
  ) VALUES (
    p_question_id,
    v_current_user_id,
    p_answer_text
  ) RETURNING id INTO v_answer_id;

  RETURN v_answer_id;
END;
$$
;

-- anchor gift_caps_to_provider
CREATE OR REPLACE FUNCTION app_provider.gift_caps_to_provider (
  p_provider_id UUID,
  p_cap_amount app_transaction.TOKEN_UNIT
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_transfer_id UUID;
BEGIN
  -- Check if user is authenticated
  IF v_current_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required to gift caps.';
  END IF;

  -- Check if user has permission to gift caps
  IF NOT app_access.has_capability('provider.caps.gift') THEN
    RAISE EXCEPTION 'Insufficient permissions to gift caps to providers.';
  END IF;

  -- Validate cap amount is positive
  IF p_cap_amount <= 0 THEN
    RAISE EXCEPTION 'Cap amount must be greater than zero.';
  END IF;

  -- Check if the provider exists and is approved
  IF NOT EXISTS (
    SELECT 1 FROM app_provider.approved_user
    WHERE user_id = p_provider_id
  ) THEN
    RAISE EXCEPTION 'Provider not found or not approved.';
  END IF;

  -- Check if gifter is not trying to gift to themselves
  IF v_current_user_id = p_provider_id THEN
    RAISE EXCEPTION 'Cannot gift caps to yourself.';
  END IF;

  -- Create a transfer record (this will trigger the transfer logic)
  INSERT INTO app_transaction.transfer (
    sender_id,
    receiver_id,
    cap_amount
  ) VALUES (
    v_current_user_id,
    p_provider_id,
    p_cap_amount
  ) RETURNING id INTO v_transfer_id;

  RETURN v_transfer_id;
END;
$$
;

-- anchor gift_soda_to_provider
CREATE OR REPLACE FUNCTION app_provider.gift_soda_to_provider (
  p_provider_id UUID,
  p_soda_amount app_transaction.TOKEN_UNIT
) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_current_user_id UUID := auth.uid();
  v_transfer_id UUID;
BEGIN
  -- Check if user is authenticated
  IF v_current_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required to gift soda.';
  END IF;

  -- Check if user has permission to gift soda
  IF NOT app_access.has_capability('provider.soda.gift') THEN
    RAISE EXCEPTION 'Insufficient permissions to gift soda to providers.';
  END IF;

  -- Validate soda amount is positive
  IF p_soda_amount <= 0 THEN
    RAISE EXCEPTION 'Soda amount must be greater than zero.';
  END IF;

  -- Check if the provider exists and is approved
  IF NOT EXISTS (
    SELECT 1 FROM app_provider.approved_user
    WHERE user_id = p_provider_id
  ) THEN
    RAISE EXCEPTION 'Provider not found or not approved.';
  END IF;

  -- Check if gifter is not trying to gift to themselves
  IF v_current_user_id = p_provider_id THEN
    RAISE EXCEPTION 'Cannot gift soda to yourself.';
  END IF;

  -- Create a transfer record (this will trigger the transfer logic)
  INSERT INTO app_transaction.transfer (
    sender_id,
    receiver_id,
    soda_amount
  ) VALUES (
    v_current_user_id,
    p_provider_id,
    p_soda_amount
  ) RETURNING id INTO v_transfer_id;

  RETURN v_transfer_id;
END;
$$
;

-- !section
-- section TRIGGERS
-- anchor service
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.service FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

CREATE TRIGGER service_set_updated_at BEFORE
UPDATE ON app_provider.service FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

CREATE TRIGGER service_delete_approved_service
AFTER INSERT
OR
UPDATE
OR DELETE ON app_provider.service FOR EACH ROW
EXECUTE FUNCTION app_core.delete_related_row (
  'app_provider',
  'approved_service',
  'id',
  'service_id'
)
;

-- anchor application
CREATE TRIGGER application_set_updated_at BEFORE
UPDATE ON app_provider.application FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

CREATE TRIGGER application_approved_trigger
AFTER INSERT
OR
UPDATE ON app_provider.application FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_application_approved ()
;

CREATE TRIGGER application_assign_provider_applicant_role
AFTER INSERT ON app_provider.application FOR EACH ROW
EXECUTE FUNCTION app_provider.assign_provider_applicant_role_trigger_function ()
;

CREATE TRIGGER application_assign_provider_applicant_under_review_role
AFTER
UPDATE ON app_provider.application FOR EACH ROW
EXECUTE FUNCTION app_provider.assign_provider_applicant_under_review_role_trigger_function ()
;

CREATE TRIGGER application_revoke_applicant_roles_on_rejection
AFTER
UPDATE OF application_status ON app_provider.application FOR EACH ROW WHEN (
  NEW.application_status = 'rejected'
  AND OLD.application_status <> 'rejected'
)
EXECUTE FUNCTION app_provider.revoke_applicant_roles_on_rejection ()
;

CREATE TRIGGER application_check_required_fields_on_submit BEFORE
UPDATE OF application_status ON app_provider.application FOR EACH ROW WHEN (
  NEW.application_status = 'submitted'
  AND OLD.application_status = 'draft'
)
EXECUTE FUNCTION app_provider.check_required_fields_on_application_submit ()
;

-- anchor approved_user
CREATE TRIGGER approved_user_assign_provider_role
AFTER INSERT ON app_provider.approved_user FOR EACH ROW
EXECUTE FUNCTION app_provider.assign_provider_role_on_approved_user_insert ()
;

CREATE TRIGGER approved_user_revoke_provider_role
AFTER DELETE ON app_provider.approved_user FOR EACH ROW
EXECUTE FUNCTION app_provider.revoke_provider_role_on_approved_user_delete ()
;

-- anchor approved_service
CREATE TRIGGER approved_service_update_activity_service_count
AFTER INSERT
OR DELETE ON app_provider.approved_service FOR EACH ROW
EXECUTE FUNCTION app_provider.update_activity_service_count_on_approval ()
;

-- anchor service_modifier
CREATE TRIGGER service_modifier_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.service_modifier FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('name', 'description')
;

CREATE TRIGGER service_modifier_delete_approved_service_modifier
AFTER INSERT
OR
UPDATE
OR DELETE ON app_provider.service_modifier FOR EACH ROW
EXECUTE FUNCTION app_core.delete_related_row (
  'app_provider',
  'approved_service_modifier',
  'id',
  'service_modifier_id'
)
;

-- anchor profile
CREATE TRIGGER profile_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.profile FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('bio')
;

-- anchor order
CREATE TRIGGER order_process_completion_and_review_pass BEFORE
UPDATE ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_provider.process_order_completion_and_review_pass ()
;

CREATE TRIGGER order_set_updated_at BEFORE
UPDATE ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

CREATE TRIGGER order_before_insert_with_escrow BEFORE INSERT ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_new_order_with_escrow ()
;

CREATE TRIGGER order_refund_on_change
AFTER DELETE
OR
UPDATE OF order_status ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_order_refund_on_update_or_delete ()
;

CREATE TRIGGER order_archive_on_delete BEFORE DELETE ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_provider.archive_order_and_update_performance ()
;

CREATE TRIGGER order_log_status_change
AFTER
UPDATE OF order_status ON app_provider.order FOR EACH ROW WHEN (
  NEW.order_status IS DISTINCT FROM OLD.order_status
)
EXECUTE FUNCTION app_provider.log_order_status_change ()
;

CREATE TRIGGER order_log_insertion
AFTER INSERT ON app_provider.order FOR EACH ROW
EXECUTE FUNCTION app_provider.log_order_insertion ()
;

CREATE TRIGGER order_update_performance_on_refund
AFTER
UPDATE OF order_status ON app_provider.order FOR EACH ROW WHEN (
  NEW.order_status = 'refunded'
  AND OLD.order_status IS DISTINCT FROM 'refunded'
)
EXECUTE FUNCTION app_provider.handle_order_refund_update_performance ()
;

-- anchor review
CREATE TRIGGER review_delete_approved_review
AFTER INSERT
OR DELETE ON app_provider.review FOR EACH ROW
EXECUTE FUNCTION app_core.delete_related_row (
  'app_provider',
  'approved_review',
  'id',
  'review_id'
)
;

CREATE TRIGGER review_reward_caps
AFTER INSERT ON app_provider.review FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_new_review_reward_caps ()
;

-- anchor config
CREATE TRIGGER config_prevent_modifications BEFORE DELETE ON app_provider.config FOR EACH ROW
EXECUTE FUNCTION app_core.prevent_delete_operation ()
;

-- anchor provider_status
CREATE TRIGGER provider_status_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.status FOR EACH ROW
EXECUTE FUNCTION app_core.validate_locale_columns ('notes')
;

CREATE TRIGGER provider_status_set_updated_at BEFORE
UPDATE ON app_provider.status FOR EACH ROW
EXECUTE FUNCTION app_core.set_updated_at ()
;

-- anchor availability
CREATE TRIGGER availability_check_overlap BEFORE INSERT
OR
UPDATE ON app_provider.availability FOR EACH ROW
EXECUTE FUNCTION app_provider.check_availability_overlap ()
;

-- anchor field_value
CREATE TRIGGER field_value_check_constraints BEFORE INSERT
OR
UPDATE ON app_provider.field_value FOR EACH ROW
EXECUTE FUNCTION app_provider.check_field_value_constraints ()
;

-- !section
-- section INDEXES
-- anchor user_favorite
CREATE INDEX idx_user_favorite_provider_id ON app_provider.user_favorite (provider_id)
;

CREATE INDEX idx_user_favorite_created_at ON app_provider.user_favorite (created_at)
;

-- !section
-- section RLS POLICIES
-- anchor config
ALTER TABLE app_provider.config ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "config_select_all" ON app_provider.config FOR
SELECT
  USING (TRUE)
;

-- anchor profile
ALTER TABLE app_provider.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select" ON app_provider.profile FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('provider.profile.all.view')
    )
  )
;

CREATE POLICY "profile_crud" ON app_provider.profile FOR ALL USING (
  (
    SELECT
      app_access.has_capability ('provider.profile.all.edit')
  )
)
WITH
  CHECK (
    (
      SELECT
        app_access.has_capability ('provider.profile.all.edit')
    )
  )
;

CREATE POLICY "profile_select_own" ON app_provider.profile FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND (
      SELECT
        app_access.has_capability ('provider.profile.view')
    )
  )
;

CREATE POLICY "profile_insert_own" ON app_provider.profile FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND (
      SELECT
        app_access.has_capability ('provider.profile.edit')
    )
  )
;

CREATE POLICY "profile_update_own" ON app_provider.profile
FOR UPDATE
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND (
      SELECT
        app_access.has_capability ('provider.profile.edit')
    )
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND (
      SELECT
        app_access.has_capability ('provider.profile.edit')
    )
  )
;

-- anchor application
ALTER TABLE app_provider.application ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "application_select" ON app_provider.application FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability (
          'provider.application.all.view'
        )
    )
  )
;

CREATE POLICY "application_update" ON app_provider.application
FOR UPDATE
  USING (
    (
      SELECT
        app_access.has_capability (
          'provider.application.all.edit'
        )
    )
  )
WITH
  CHECK (
    (
      SELECT
        app_access.has_capability (
          'provider.application.all.edit'
        )
    )
  )
;

CREATE POLICY "application_select_own" ON app_provider.application FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND (
      SELECT
        app_access.has_capability ('provider.application.view')
    )
  )
;

CREATE POLICY "application_insert_own_draft" ON app_provider.application FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND application_status = 'draft'
  )
;

CREATE POLICY "application_update_own_draft" ON app_provider.application
FOR UPDATE
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND application_status = 'draft'
    AND (
      SELECT
        app_access.has_capability ('provider.application.edit')
    )
  )
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND application_status IN ('draft', 'submitted')
    AND (
      SELECT
        app_access.has_capability ('provider.application.edit')
    )
  )
;

-- anchor order
ALTER TABLE app_provider.order ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "order_select_sender_receiver" ON app_provider.order FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) IN (sender_id, receiver_id)
  )
;

CREATE POLICY "order_select" ON app_provider.order FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('provider.order.all.view')
    )
  )
;

-- anchor order_archive
ALTER TABLE app_provider.order_archive ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "order_archive_select_sender_receiver" ON app_provider.order_archive FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) IN (sender_id, receiver_id)
  )
;

-- anchor activity
ALTER TABLE app_provider.activity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_select_all" ON app_provider.activity FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "activity_crud_own" ON app_provider.activity FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.activity.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.activity.edit')
  )
;

CREATE POLICY "activity_select" ON app_provider.activity FOR
SELECT
  USING (
    app_access.has_capability ('provider.activity.all.view')
  )
;

CREATE POLICY "activity_crud" ON app_provider.activity FOR ALL USING (
  app_access.has_capability ('provider.activity.all.edit')
)
WITH
  CHECK (
    app_access.has_capability ('provider.activity.all.edit')
  )
;

-- anchor service
ALTER TABLE app_provider.service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_select_approved" ON app_provider.service FOR
SELECT
  USING (
    status = 'published'
    AND app_provider.is_user_approved (user_id)
    AND app_provider.is_service_approved (id)
  )
;

CREATE POLICY "service_select_own" ON app_provider.service FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.service.view')
  )
;

CREATE POLICY "service_crud_own" ON app_provider.service FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.service.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.service.edit')
  )
;

-- anchor service_modifier
ALTER TABLE app_provider.service_modifier ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_modifier_select_approved" ON app_provider.service_modifier FOR
SELECT
  USING (
    status = 'published'
    AND app_provider.is_user_approved (user_id)
    AND app_provider.is_service_modifier_approved (id)
  )
;

CREATE POLICY "service_modifier_select_own" ON app_provider.service_modifier FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability (
      'provider.service_modifier.view'
    )
  )
;

CREATE POLICY "service_modifier_crud_own" ON app_provider.service_modifier FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability (
    'provider.service_modifier.edit'
  )
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability (
      'provider.service_modifier.edit'
    )
  )
;

-- anchor review_pass
ALTER TABLE app_provider.review_pass ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "review_pass_select_own" ON app_provider.review_pass FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

-- anchor review
ALTER TABLE app_provider.review ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "review_select" ON app_provider.review FOR
SELECT
  USING (
    app_access.has_capability ('provider.review.all.view')
  )
;

CREATE POLICY "review_crud" ON app_provider.review FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.review.all.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.review.all.edit')
  )
;

CREATE POLICY "review_select_approved" ON app_provider.review FOR
SELECT
  USING (
    app_provider.is_review_approved (id)
  )
;

CREATE POLICY "review_select_own" ON app_provider.review FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.review.view')
  )
;

CREATE POLICY "review_insert_own" ON app_provider.review FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.review.submit')
  )
;

-- anchor approved_user
ALTER TABLE app_provider.approved_user ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "approved_user_select_all" ON app_provider.approved_user FOR
SELECT
  USING (TRUE)
;

-- anchor approved_service
ALTER TABLE app_provider.approved_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "approved_service_select_all" ON app_provider.approved_service FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "approved_service_insert" ON app_provider.approved_service FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'provider.service.all.approve'
    )
  )
;

CREATE POLICY "approved_service_delete" ON app_provider.approved_service FOR DELETE USING (
  app_access.has_capability (
    'provider.service.all.disapprove'
  )
)
;

-- anchor approved_service_modifier
ALTER TABLE app_provider.approved_service_modifier ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "approved_service_modifier_select_all" ON app_provider.approved_service_modifier FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "approved_service_modifier_insert" ON app_provider.approved_service_modifier FOR INSERT
WITH
  CHECK (
    app_access.has_capability (
      'provider.service_modifier.all.approve'
    )
  )
;

CREATE POLICY "approved_service_modifier_delete" ON app_provider.approved_service_modifier FOR DELETE USING (
  app_access.has_capability (
    'provider.service_modifier.all.disapprove'
  )
)
;

-- anchor approved_review
ALTER TABLE app_provider.approved_review ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "approved_review_select_all" ON app_provider.approved_review FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "approved_review_insert" ON app_provider.approved_review FOR INSERT
WITH
  CHECK (
    app_access.has_capability ('provider.review.all.approve')
  )
;

CREATE POLICY "approved_review_delete" ON app_provider.approved_review FOR DELETE USING (
  app_access.has_capability (
    'provider.review.all.disapprove'
  )
)
;

CREATE TRIGGER approved_review_update_performance
AFTER INSERT ON app_provider.approved_review FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_approved_review_update_performance ()
;

-- anchor question
CREATE TRIGGER question_handle_cap_deduction BEFORE INSERT ON app_provider.question FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_new_question ()
;

-- anchor question_answer
CREATE TRIGGER question_answer_handle_cap_reward BEFORE INSERT ON app_provider.question_answer FOR EACH ROW
EXECUTE FUNCTION app_provider.handle_new_question_answer ()
;

-- anchor performance
ALTER TABLE app_provider.performance ENABLE ROW LEVEL SECURITY
;

-- anchor activity_performance
ALTER TABLE app_provider.activity_performance ENABLE ROW LEVEL SECURITY
;

-- anchor status
ALTER TABLE app_provider.status ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "provider_status_select_all" ON app_provider.status FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "provider_status_crud_own" ON app_provider.status FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.status.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.status.edit')
  )
;

-- anchor order_log
ALTER TABLE app_provider.order_log ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "order_log_select_all" ON app_provider.order_log FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability ('provider.order.all.view')
    )
  )
;

-- anchor availability
ALTER TABLE app_provider.availability ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "availability_select_approved" ON app_provider.availability FOR
SELECT
  USING (
    app_provider.is_user_approved (user_id)
  )
;

CREATE POLICY "availability_select_all" ON app_provider.availability FOR
SELECT
  USING (
    (
      SELECT
        app_access.has_capability (
          'provider.availability.all.view'
        )
    )
  )
;

CREATE POLICY "availability_crud_all" ON app_provider.availability FOR ALL USING (
  (
    SELECT
      app_access.has_capability (
        'provider.availability.all.edit'
      )
  )
)
WITH
  CHECK (
    (
      SELECT
        app_access.has_capability (
          'provider.availability.all.edit'
        )
    )
  )
;

CREATE POLICY "availability_select_own" ON app_provider.availability FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.availability.view')
  )
;

CREATE POLICY "availability_crud_own" ON app_provider.availability FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.availability.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.availability.edit')
  )
;

-- anchor field_value
ALTER TABLE app_provider.field_value ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "field_value_select_all" ON app_provider.field_value FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "field_value_crud_own" ON app_provider.field_value FOR ALL USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
  AND app_access.has_capability ('provider.field_value.edit')
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
    AND app_access.has_capability ('provider.field_value.edit')
  )
;

CREATE POLICY "field_value_crud_admin" ON app_provider.field_value FOR ALL USING (
  app_access.has_capability (
    'provider.field_value.all.edit'
  )
)
WITH
  CHECK (
    app_access.has_capability (
      'provider.field_value.all.edit'
    )
  )
;

-- anchor user_favorite
ALTER TABLE app_provider.user_favorite ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "user_favorite_select_own" ON app_provider.user_favorite FOR
SELECT
  USING (
    user_id = auth.uid ()
    AND app_access.has_capability ('provider.user_favorite.view')
  )
;

CREATE POLICY "user_favorite_insert_own" ON app_provider.user_favorite FOR INSERT
WITH
  CHECK (
    user_id = auth.uid ()
    AND app_access.has_capability ('provider.user_favorite.edit')
  )
;

CREATE POLICY "user_favorite_delete_own" ON app_provider.user_favorite FOR DELETE USING (
  user_id = auth.uid ()
  AND app_access.has_capability ('provider.user_favorite.edit')
)
;

CREATE POLICY "user_favorite_select_admin" ON app_provider.user_favorite FOR
SELECT
  USING (
    app_access.has_capability (
      'provider.user_favorite.all.view'
    )
  )
;

CREATE POLICY "user_favorite_manage_admin" ON app_provider.user_favorite FOR ALL USING (
  app_access.has_capability (
    'provider.user_favorite.all.edit'
  )
)
WITH
  CHECK (
    app_access.has_capability (
      'provider.user_favorite.all.edit'
    )
  )
;

-- anchor question
ALTER TABLE app_provider.question ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "question_select_own" ON app_provider.question FOR
SELECT
  USING (
    (
      asker_id = auth.uid ()
      OR provider_id = auth.uid ()
    )
    AND app_access.has_capability ('provider.question.view')
  )
;

CREATE POLICY "question_insert_own" ON app_provider.question FOR INSERT
WITH
  CHECK (
    asker_id = auth.uid ()
    AND app_access.has_capability ('provider.question.submit')
  )
;

CREATE POLICY "question_manage_admin" ON app_provider.question FOR ALL USING (
  app_access.has_capability ('provider.question.all.view')
)
WITH
  CHECK (
    app_access.has_capability ('provider.question.all.edit')
  )
;

-- anchor question_answer
ALTER TABLE app_provider.question_answer ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "question_answer_select_related" ON app_provider.question_answer FOR
SELECT
  USING (
    EXISTS (
      SELECT
        1
      FROM
        app_provider.question pq
      WHERE
        pq.id = question_id
        AND (
          pq.asker_id = auth.uid ()
          OR pq.provider_id = auth.uid ()
        )
    )
    AND app_access.has_capability ('provider.question.view')
  )
;

CREATE POLICY "question_answer_insert_own" ON app_provider.question_answer FOR INSERT
WITH
  CHECK (
    provider_id = auth.uid ()
    AND app_access.has_capability ('provider.question.answer')
  )
;

CREATE POLICY "question_answer_manage_admin" ON app_provider.question_answer FOR ALL USING (
  app_access.has_capability ('provider.question.all.view')
)
WITH
  CHECK (
    app_access.has_capability ('provider.question.all.edit')
  )
;

-- !section
-- section CRONJOBS
-- anchor release_completed_order_escrow
SELECT
  cron.schedule (
    'release_completed_order_escrow',
    '* * * * *', -- schedule (every minute)
    'SELECT app_provider.release_completed_order_escrow();'
  )
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'provider.application.all.view',
      'provider.application.all.edit',
      'provider.profile.all.view',
      'provider.profile.all.edit',
      'provider.service.all.view',
      'provider.service.all.edit',
      'provider.activity.all.view',
      'provider.activity.all.edit',
      'provider.service.all.approve',
      'provider.service.all.disapprove',
      'provider.service_modifier.all.view',
      'provider.service_modifier.all.edit',
      'provider.service_modifier.all.approve',
      'provider.service_modifier.all.disapprove',
      'provider.field_value.all.view',
      'provider.field_value.all.edit',
      'provider.order.all.view',
      'provider.order.all.control',
      'provider.review.all.view',
      'provider.review.all.edit',
      'provider.review.all.approve',
      'provider.review.all.disapprove',
      'provider.availability.all.view',
      'provider.availability.all.edit',
      'provider.user_favorite.all.view',
      'provider.user_favorite.all.edit',
      'provider.question.all.view',
      'provider.question.all.edit'
    ]
  )
;

-- anchor customer
SELECT
  app_access.define_role_capability (
    'customer',
    ARRAY[
      'provider.order.view',
      'provider.order.submit',
      'provider.review.view',
      'provider.review.submit',
      'provider.user_favorite.view',
      'provider.user_favorite.edit',
      'provider.question.submit',
      'provider.question.view',
      'provider.caps.gift',
      'provider.soda.gift'
    ]
  )
;

-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'provider.profile.view',
      'provider.profile.edit',
      'provider.activity.edit',
      'provider.service.view',
      'provider.service.edit',
      'provider.service_modifier.view',
      'provider.service_modifier.edit',
      'provider.field_value.view',
      'provider.field_value.edit',
      'provider.order.view',
      'provider.order.submit',
      'provider.review.view',
      'provider.review.submit',
      'provider.status.edit',
      'provider.availability.view',
      'provider.availability.edit',
      'provider.user_favorite.view',
      'provider.user_favorite.edit',
      'provider.question.answer',
      'provider.question.view',
      'provider.caps.gift',
      'provider.soda.gift'
    ]
  )
;

-- anchor provider_applicant
SELECT
  app_access.define_role_capability (
    'provider_applicant',
    ARRAY[
      'provider.application.view',
      'provider.application.edit',
      'provider.profile.view',
      'provider.profile.edit',
      'provider.activity.edit',
      'provider.service.view',
      'provider.service.edit',
      'provider.service_modifier.view',
      'provider.service_modifier.edit',
      'provider.field_value.view',
      'provider.field_value.edit',
      'provider.availability.view',
      'provider.availability.edit'
    ]
  )
;

-- anchor provider_applicant_under_review
SELECT
  app_access.define_role_capability (
    'provider_applicant_under_review',
    ARRAY[
      'provider.application.view',
      'provider.profile.view',
      'provider.service.view',
      'provider.service_modifier.view',
      'provider.field_value.view',
      'provider.availability.view'
    ]
  )
;

-- !section