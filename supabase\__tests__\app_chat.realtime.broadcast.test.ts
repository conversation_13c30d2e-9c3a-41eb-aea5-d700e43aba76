import { afterAll, beforeAll, describe, expect, test } from "vitest";
import {
  MockConversation,
  mockConversation
} from "./mocks/app_chat.conversation";
import { mockCustomer, mockProvider, MockUser } from "./mocks/auth.user";
import { createMessage, mockMessage } from "./mocks/app_chat.message";
import { RealtimeChannel } from "@supabase/supabase-js";
import { serviceClient } from "./utils/client";

type ChannelParams = {
  user: MockUser;
  conversation: MockConversation;
  insertedMessages?: string[];
  updatedMessages?: string[];
  deletedMessages?: string[];
  logs?: string[];
  reads?: string[];
};

async function createChannel({
  user,
  conversation,
  insertedMessages = [],
  updatedMessages = [],
  deletedMessages = [],
  logs = [],
  reads = []
}: ChannelParams) {
  if (!user.client) throw new Error("Customer client is undefined");

  await user.client.realtime.setAuth();

  return await new Promise<RealtimeChannel>((resolve, reject) => {
    if (!user.client) throw new Error("Customer client is undefined");
    if (!conversation.id) throw new Error("Conversation ID is undefined");

    const channel = user.client
      .channel(`chat:${conversation.id}`, {
        config: { private: true, broadcast: { ack: true } }
      })
      .on("broadcast", { event: "INSERT" }, ({ payload }) => {
        if (payload.content)
          insertedMessages.push(
            typeof payload.content === "string"
              ? payload.content
              : JSON.stringify(payload.content)
          );
      })
      .on("broadcast", { event: "UPDATE" }, ({ payload }) => {
        if (payload.content)
          updatedMessages.push(
            typeof payload.content === "string"
              ? payload.content
              : JSON.stringify(payload.content)
          );
      })
      .on("broadcast", { event: "DELETE" }, ({ payload }) => {
        if (payload.content)
          deletedMessages.push(
            typeof payload.content === "string"
              ? payload.content
              : JSON.stringify(payload.content)
          );
      })
      .on("broadcast", { event: "LOG" }, ({ payload }) => {
        if (payload.action) logs.push(payload.action);
      })
      .on("broadcast", { event: "READ" }, ({ payload }) => {
        if (payload.user_id) reads.push(payload.user_id);
      })
      .subscribe(async (status) => {
        console.log(status);
        if (status === "SUBSCRIBED") resolve(channel);
        if (status === "CHANNEL_ERROR") reject();
      });
  });
}

const customer = mockCustomer();

describe("user", () => {
  const provider = mockProvider();
  const conversation = mockConversation({
    members: [customer, provider]
  });
  const conversationIds: string[] = [];
  const insertedMessages: string[] = [];
  const updatedMessages: string[] = [];
  const deletedMessages: string[] = [];
  const logs: string[] = [];
  const reads: string[] = [];

  beforeAll(async () => {
    if (conversation.id) conversationIds.push(conversation.id);
    await createChannel({
      user: customer,
      conversation,
      insertedMessages,
      updatedMessages,
      deletedMessages,
      logs,
      reads
    });

    // retry this until receiving the health check message
    for (let i = 0; i < 50; i++) {
      await createMessage({
        sender: customer,
        conversation: conversation,
        content: "HEALTH CHECK"
      });

      await new Promise((resolve) => setTimeout(resolve, 250));

      if (insertedMessages.find((message) => message === "HEALTH CHECK")) {
        break;
      }
    }
  }, 50000);
  test("can receive messages", async () => {
    await createMessage({
      sender: customer,
      conversation: conversation,
      content: "Test message from customer"
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(
      insertedMessages.find(
        (message) => message === "Test message from customer"
      )
    ).toBe("Test message from customer");

    await createMessage({
      sender: provider,
      conversation: conversation,
      content: "Test message from provider"
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(
      insertedMessages.find(
        (message) => message === "Test message from provider"
      )
    ).toBe("Test message from provider");
  });

  test("can send media messages", async () => {
    await createMessage({
      sender: customer,
      conversation: conversation,
      content: { media: "test" }
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(
      insertedMessages.find((message) => message === '{"media":"test"}')
    ).toBe('{"media":"test"}');
  });

  const updatedMessage = mockMessage({
    sender: customer,
    conversation: conversation,
    content: "Waiting for update"
  });

  test("can receive updated messages", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!updatedMessage.id) throw new Error("Updated message ID is undefined");

    await customer.client
      .schema("app_chat")
      .from("message")
      .update({ content: "Updated message" })
      .eq("id", updatedMessage.id);

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(
      updatedMessages.find((message) => message === "Updated message")
    ).toBe("Updated message");
  });

  const deletedMessage = mockMessage({
    sender: customer,
    conversation: conversation,
    content: "Waiting for delete"
  });

  test("can receive deleted messages", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!deletedMessage.id) throw new Error("Updated message ID is undefined");

    await customer.client
      .schema("app_chat")
      .from("message")
      .delete()
      .eq("id", deletedMessage.id);

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(
      deletedMessages.filter((message) => message === "Waiting for delete")
        .length
    ).toBe(1);
  });

  test("can receive logs", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!conversation.id) throw new Error("Conversation ID is undefined");

    await serviceClient.schema("app_chat").from("log").insert({
      conversation_id: conversation.id,
      user_id: customer.data.id,
      action: "test_action"
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(logs.find((log) => log === "test_action")).toBe("test_action");
  });

  const readMessage = mockMessage({
    sender: customer,
    conversation: conversation,
    content: "Waiting for read"
  });

  test("can receive reads", async () => {
    if (!customer.data) throw new Error("Customer data is undefined");
    if (!conversation.id) throw new Error("Conversation ID is undefined");
    if (!readMessage.id) throw new Error("Read message ID is undefined");

    await serviceClient.schema("app_chat").from("message_read").insert({
      message_id: readMessage.id,
      user_id: customer.data.id
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(reads.find((read) => read === customer.data?.id)).toBe(
      customer.data.id
    );
  });

  const customer2 = mockCustomer();
  const conversation2 = mockConversation({
    members: [customer2, provider]
  });
  if (conversation2.id) conversationIds.push(conversation2.id);

  test("cannot connect to a conversation they are not part of", async () => {
    await expect(
      createChannel({
        user: customer,
        conversation: conversation2,
        insertedMessages: [],
        updatedMessages: [],
        deletedMessages: [],
        logs
      })
    ).rejects.toThrow();
  });

  test("receives a log when they block a user", async () => {
    if (!customer.client) throw new Error("Customer client is undefined");
    if (!provider.data) throw new Error("Provider data is undefined");

    await customer.client.schema("app_account").from("user_block").insert({
      blocked_id: provider.data.id
    });

    await new Promise((resolve) => setTimeout(resolve, 250));

    expect(logs.find((log) => log === "user.block")).toBe("user.block");
  });

  afterAll(async () => {
    await serviceClient
      .schema("app_chat")
      .from("message")
      .delete()
      .in("conversation_id", conversationIds);

    await serviceClient
      .schema("app_chat")
      .from("deleted_message")
      .delete()
      .in("conversation_id", conversationIds);

    await serviceClient
      .schema("app_chat")
      .from("conversation")
      .delete()
      .in("id", conversationIds);
  });
});
