import { describe, test, expect } from "vitest";
import { mockAdmin, mockCustomer, mockProvider } from "./mocks/auth.user";
import { createSetupHooks } from "./utils/createSetupHooks";
import { serviceClient } from "./utils/client";
import { mockUserFavoriteEach } from "./mocks/app_provider.user_favorite";

createSetupHooks();

describe("Customer Provider Favoriting", () => {
  const customer1 = mockCustomer();
  const customer2 = mockCustomer();
  const provider1 = mockProvider();

  test("customer can favorite a provider", async () => {
    if (!customer1.client || !customer1.data || !provider1.data)
      throw new Error("Customer or provider not defined");

    const { data, error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer1.data.id,
        provider_id: provider1.data.id
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(customer1.data.id);
    expect(data?.provider_id).toBe(provider1.data.id);
    expect(data?.created_at).toBeDefined();
  });

  test("customer can view their own favorites", async () => {
    if (!customer1.client || !customer1.data || !provider1.data)
      throw new Error("Customer or provider not defined");

    // First add a favorite
    await customer1.client.schema("app_provider").from("user_favorite").insert({
      user_id: customer1.data.id,
      provider_id: provider1.data.id
    });

    const { data, error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", customer1.data.id);

    expect(error).toBeNull();
    expect(data?.length).toBeGreaterThan(0);
    expect(data?.[0]?.provider_id).toBe(provider1.data.id);
  });

  test("customer can remove their favorite", async () => {
    if (!customer1.client || !customer1.data || !provider1.data)
      throw new Error("Customer or provider not defined");

    // First add a favorite
    await customer1.client.schema("app_provider").from("user_favorite").insert({
      user_id: customer1.data.id,
      provider_id: provider1.data.id
    });

    const { error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .delete()
      .eq("user_id", customer1.data.id)
      .eq("provider_id", provider1.data.id);

    expect(error).toBeNull();

    // Verify it's deleted
    const { data } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", customer1.data.id)
      .eq("provider_id", provider1.data.id);

    expect(data?.length).toBe(0);
  });

  test("customer cannot favorite the same provider twice", async () => {
    if (!customer1.client || !customer1.data || !provider1.data)
      throw new Error("Customer or provider not defined");

    // First favorite
    await customer1.client.schema("app_provider").from("user_favorite").insert({
      user_id: customer1.data.id,
      provider_id: provider1.data.id
    });

    // Second favorite should fail
    const { error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer1.data.id,
        provider_id: provider1.data.id
      });

    expect(error).not.toBeNull();
    expect(error?.code).toBe("23505"); // Unique constraint violation
  });

  test("customer cannot favorite themselves as a provider", async () => {
    if (!customer1.client || !customer1.data)
      throw new Error("Customer not defined");

    const { error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer1.data.id,
        provider_id: customer1.data.id
      });

    expect(error).not.toBeNull();
    expect(error?.code).toBe("23514"); // Check constraint violation
  });

  test("customer cannot view other customers' favorites", async () => {
    if (!customer1.client || !customer2.data)
      throw new Error("Customer not defined");

    const { data, error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", customer2.data.id);

    expect(error).toBeNull();
    expect(data?.length).toBe(0);
  });

  test("customer cannot create favorites for other customers", async () => {
    if (!customer1.client || !customer2.data || !provider1.data)
      throw new Error("Customer or provider not defined");

    const { error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer2.data.id,
        provider_id: provider1.data.id
      });

    expect(error).not.toBeNull();
  });

  test("customer cannot delete other customers' favorites", async () => {
    if (
      !customer1.client ||
      !customer2.client ||
      !customer2.data ||
      !provider1.data
    )
      throw new Error("Customer or provider not defined");

    // Customer2 creates a favorite
    await customer2.client.schema("app_provider").from("user_favorite").insert({
      user_id: customer2.data.id,
      provider_id: provider1.data.id
    });

    // Customer1 tries to delete it
    const { error } = await customer1.client
      .schema("app_provider")
      .from("user_favorite")
      .delete()
      .eq("user_id", customer2.data.id)
      .eq("provider_id", provider1.data.id);

    expect(error).toBeNull(); // No error but should not delete

    // Verify it still exists
    const { data } = await serviceClient
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", customer2.data.id)
      .eq("provider_id", provider1.data.id);

    expect(data?.length).toBe(1);
  });
});

describe("Provider Favoriting", () => {
  const customer = mockCustomer();
  const provider = mockProvider();
  const provider2 = mockProvider();

  test("provider can view their own favorites", async () => {
    if (!provider.client || !provider.data || !customer.data)
      throw new Error("Provider or customer not defined");

    // Customer favorites the provider
    await serviceClient.schema("app_provider").from("user_favorite").insert({
      user_id: customer.data.id,
      provider_id: provider.data.id
    });

    const { data, error } = await provider.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", provider.data.id);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  });

  test("provider can favorite other providers", async () => {
    if (!provider.client || !provider.data || !provider2.data)
      throw new Error("Provider not defined");

    const { data, error } = await provider.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: provider.data.id,
        provider_id: provider2.data.id
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(provider.data.id);
    expect(data?.provider_id).toBe(provider2.data.id);
  });
});

describe("Admin Provider Favoriting", () => {
  const admin = mockAdmin();
  const customer = mockCustomer();
  const provider = mockProvider();
  const customer2 = mockCustomer();
  mockUserFavoriteEach({ customer, provider });

  test("admin can view all favorites", async () => {
    if (!admin.client || !customer.data || !provider.data)
      throw new Error("Admin, customer, or provider not defined");

    const { data, error } = await admin.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*");

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(data?.length).toBeGreaterThan(0);

    const customerFavorite = data?.find(
      (fav) =>
        fav.user_id === customer.data?.id &&
        fav.provider_id === provider.data?.id
    );
    expect(customerFavorite).toBeDefined();
  });

  test("admin can create favorites for any user", async () => {
    if (!admin.client || !customer2.data || !provider.data)
      throw new Error("Admin, customer, or provider not defined");

    const { data, error } = await admin.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer2.data.id,
        provider_id: provider.data.id
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.user_id).toBe(customer2.data.id);
    expect(data?.provider_id).toBe(provider.data.id);
  });

  test("admin can delete any favorite", async () => {
    if (!admin.client || !customer.data || !provider.data)
      throw new Error("Admin, customer, or provider not defined");

    const { error } = await admin.client
      .schema("app_provider")
      .from("user_favorite")
      .delete()
      .eq("user_id", customer.data.id)
      .eq("provider_id", provider.data.id);

    expect(error).toBeNull();

    // Verify it's deleted
    const { data } = await admin.client
      .schema("app_provider")
      .from("user_favorite")
      .select("*")
      .eq("user_id", customer.data.id)
      .eq("provider_id", provider.data.id);

    expect(data?.length).toBe(0);
  });
});

describe("Constraint Validation", () => {
  const customer = mockCustomer();
  const customer2 = mockCustomer();
  const provider = mockProvider();

  test("primary key constraint prevents duplicate favorites", async () => {
    if (!customer.client || !customer.data || !provider.data)
      throw new Error("Customer or provider not defined");

    // First favorite should succeed
    await customer.client.schema("app_provider").from("user_favorite").insert({
      user_id: customer.data.id,
      provider_id: provider.data.id
    });

    // Second favorite should fail with unique constraint violation
    const { error } = await customer.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer.data.id,
        provider_id: provider.data.id
      });

    expect(error).not.toBeNull();
    expect(error?.code).toBe("23505"); // Unique constraint violation
  });

  test("created_at is automatically set", async () => {
    if (!customer2.client || !customer2.data || !provider.data)
      throw new Error("Customer or provider not defined");

    const { data, error } = await customer2.client
      .schema("app_provider")
      .from("user_favorite")
      .insert({
        user_id: customer2.data.id,
        provider_id: provider.data.id
      })
      .select()
      .single();

    expect(error).toBeNull();
    expect(data?.created_at).toBeDefined();
    expect(new Date(data?.created_at ?? "").getTime()).toBeLessThanOrEqual(
      Date.now()
    );
  });
});
